/**
 * Response Classification Module
 * 
 * This module handles Korean user response classification using GPT-based analysis.
 * Optimized for Korean input with robust JSON parsing and fallback mechanisms.
 */

import { PATTERNS, OPENAI_CONFIG } from './config';
import { getOpenAIClient } from '../openai-client';

// ============================================================================
// Types
// ============================================================================

export interface ResponseClassificationResult {
  type: 'positive' | 'negative' | 'neutral';
  confidence: number;
  reasoning: string;
}

// ============================================================================
// JSON Parsing Utilities
// ============================================================================

/**
 * Robust JSON parsing with Korean text handling
 */
function parseJSONWithFallback(jsonString: string): any {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    // Enhanced fallback: handle truncated Korean text and malformed JSON
    try {
      // Try to fix common JSON issues before parsing
      let fixedResponse = jsonString;

      // Fix unterminated strings in reasoning field (common with Korean text)
      const reasoningMatch = fixedResponse.match(/"reasoning"\s*:\s*"([^"]*?)(?:$|")/);
      if (reasoningMatch && !fixedResponse.includes('"reasoning":"' + reasoningMatch[1] + '"')) {
        // If reasoning field is unterminated, close it
        fixedResponse = fixedResponse.replace(
          /"reasoning"\s*:\s*"([^"]*?)$/,
          '"reasoning":"$1"'
        );
        // Ensure proper JSON closure
        if (!fixedResponse.trim().endsWith('}')) {
          fixedResponse = fixedResponse.trim() + '}';
        }
      }

      // Try parsing the fixed response
      const parsed = JSON.parse(fixedResponse);
      console.log('✅ JSON parsing succeeded with fallback fix');
      return parsed;
    } catch (fallbackError) {
      console.error('❌ JSON parsing failed even with fallback:', fallbackError);
      throw new Error(`JSON parsing failed: ${error}`);
    }
  }
}

// ============================================================================
// GPT-based Classification Functions
// ============================================================================

/**
 * GPT-based response type classification optimized for Korean input
 */
async function classifyResponseType(text: string): Promise<ResponseClassificationResult> {
  const prompt = `다음 사용자 응답이 긍정적인지, 부정적인지, 중립적인지 분류해주세요.

사용자 응답: "${text}"

분류 기준:
- 긍정적: 네, 좋아요, 맞아요, 그래요, 알겠어요, 원해요, 관심있어요 등
- 부정적: 아니요, 싫어요, 안돼요, 별로예요, 관심없어요, 그만해요 등  
- 중립적: 모르겠어요, 글쎄요, 그냥, 상관없어요 등

응답 형식 (JSON):
{
  "type": "positive|negative|neutral",
  "confidence": 0.0-1.0,
  "reasoning": "간단한 판단 이유"
}`;

  const response = await getOpenAIClient('RESPONSE_CLASSIFICATION').chat.completions.create({
    model: OPENAI_CONFIG.model,
    messages: [
      {
        role: 'system',
        content: '당신은 한국어 응답 분류 전문가입니다. 주어진 입력을 정확하게 분류하고 완전한 JSON 형식으로만 응답하세요.'
      },
      {
        role: 'user',
        content: prompt
      }
    ],
    temperature: OPENAI_CONFIG.temperature.classification,
    max_tokens: OPENAI_CONFIG.maxTokens.classification,
  });

  const aiResponse = response.choices[0].message.content?.trim();

  if (!aiResponse) {
    // Default to neutral if GPT fails
    return {
      type: 'neutral',
      confidence: 0.5,
      reasoning: 'GPT 응답 실패로 인한 기본 중립 분류'
    };
  }

  try {
    const result = parseJSONWithFallback(aiResponse);
    
    // Validate the result structure
    if (!result.type || !['positive', 'negative', 'neutral'].includes(result.type)) {
      throw new Error('Invalid response type');
    }

    return {
      type: result.type,
      confidence: typeof result.confidence === 'number' ? result.confidence : 0.7,
      reasoning: result.reasoning || '분류 완료'
    };
  } catch (error) {
    console.error('GPT response classification parsing failed:', error);
    console.error('Raw GPT response:', aiResponse);
    
    // Fallback to pattern matching
    const fallbackResult = classifyWithPatterns(text);
    console.log(`Fallback pattern classification: ${fallbackResult.type} (${(fallbackResult.confidence * 100).toFixed(0)}%)`);
    
    return fallbackResult;
  }
}

/**
 * Pattern-based fallback classification for Korean responses
 */
function classifyWithPatterns(text: string): ResponseClassificationResult {
  const trimmedText = text.trim();
  
  if (PATTERNS.positive.test(trimmedText)) {
    return {
      type: 'positive',
      confidence: 0.8,
      reasoning: '패턴 매칭으로 긍정 분류'
    };
  }
  
  if (PATTERNS.negative.test(trimmedText)) {
    return {
      type: 'negative',
      confidence: 0.8,
      reasoning: '패턴 매칭으로 부정 분류'
    };
  }
  
  return {
    type: 'neutral',
    confidence: 0.6,
    reasoning: '패턴 매칭으로 중립 분류'
  };
}

// ============================================================================
// Public API Functions
// ============================================================================

/**
 * Checks if text matches positive response patterns
 */
export async function isPositive(text: string): Promise<boolean> {
  try {
    const result = await classifyResponseType(text);
    return result.type === 'positive';
  } catch (error) {
    console.error('GPT positive response classification failed, using fallback:', error);
    // Fallback to basic pattern matching if GPT fails
    return PATTERNS.positive.test(text.trim());
  }
}

/**
 * Checks if text matches negative response patterns
 */
export async function isNegative(text: string): Promise<boolean> {
  try {
    const result = await classifyResponseType(text);
    return result.type === 'negative';
  } catch (error) {
    console.error('GPT negative response classification failed, using fallback:', error);
    // Fallback to basic pattern matching if GPT fails
    return PATTERNS.negative.test(text.trim());
  }
}

/**
 * Determines response type based on user input
 */
export async function getResponseType(text: string): Promise<'positive' | 'negative' | 'neutral'> {
  try {
    const result = await classifyResponseType(text);
    return result.type;
  } catch (error) {
    console.error('GPT response type classification failed, using fallback:', error);
    // Fallback to basic pattern matching if GPT fails
    if (PATTERNS.positive.test(text.trim())) return 'positive';
    if (PATTERNS.negative.test(text.trim())) return 'negative';
    return 'neutral';
  }
}

/**
 * Get detailed classification result with confidence and reasoning
 */
export async function getDetailedResponseClassification(text: string): Promise<ResponseClassificationResult> {
  return await classifyResponseType(text);
}
