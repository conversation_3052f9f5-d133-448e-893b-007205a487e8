/**
 * Recommendation Generator Module
 * 
 * This module handles investment recommendation generation and formatting.
 * Optimized for Korean display with translation support.
 */

import { QUICK_ENRICHED_FINAL as DATA } from '@/data/sp500_enriched_final';
import { RandomRecommendation, CompanyRecommendation, CompanyData } from './types';
import { translateDescription } from './ai-service';
import { getAllIndustries, getCompaniesByIndustry } from './company-data';

// ============================================================================
// Types
// ============================================================================

export interface FormattedRecommendation extends CompanyRecommendation {
  translatedDescription: string;
}

// ============================================================================
// Recommendation Generation Functions
// ============================================================================

/**
 * Generates random investment recommendations from a random industry
 */
export function generateRandomRecommendation(): RandomRecommendation {
  const industries = getAllIndustries();
  const randomIndustry = industries[Math.floor(Math.random() * industries.length)];
  
  // Get all companies in the selected industry
  const industryCompanies = getCompaniesByIndustry(randomIndustry);
  
  // Randomly select 3 companies from the industry
  const selectedCompanies = industryCompanies
    .sort(() => Math.random() - 0.5)
    .slice(0, 3)
    .map(({ ticker, data }) => ({
      ticker,
      name: data.name,
      description: data.description
    }));

  return {
    industry: randomIndustry,
    companies: selectedCompanies
  };
}

/**
 * Generates recommendations for a specific industry
 */
export function generateIndustryRecommendation(industry: string, count: number = 3): RandomRecommendation | null {
  const industryCompanies = getCompaniesByIndustry(industry);
  
  if (industryCompanies.length === 0) {
    return null;
  }
  
  // Randomly select companies from the industry
  const selectedCompanies = industryCompanies
    .sort(() => Math.random() - 0.5)
    .slice(0, Math.min(count, industryCompanies.length))
    .map(({ ticker, data }) => ({
      ticker,
      name: data.name,
      description: data.description
    }));

  return {
    industry,
    companies: selectedCompanies
  };
}

/**
 * Gets top companies by industry (by name length as a simple metric)
 */
export function getTopCompaniesByIndustry(industry: string, count: number = 5): CompanyRecommendation[] {
  const industryCompanies = getCompaniesByIndustry(industry);
  
  // Sort by description length (longer descriptions might indicate more established companies)
  return industryCompanies
    .sort((a, b) => b.data.description.length - a.data.description.length)
    .slice(0, count)
    .map(({ ticker, data }) => ({
      ticker,
      name: data.name,
      description: data.description
    }));
}

// ============================================================================
// Translation and Formatting Functions
// ============================================================================

/**
 * Translates and formats company recommendations for Korean display
 */
export async function translateAndFormatRecommendations(
  recommendations: CompanyRecommendation[]
): Promise<FormattedRecommendation[]> {
  const translatedCompanies = await Promise.all(
    recommendations.map(async (company) => ({
      ...company,
      translatedDescription: await translateDescription(company.description)
    }))
  );
  
  return translatedCompanies;
}

/**
 * Formats company descriptions for Korean display (avoiding name duplication)
 */
export function formatCompanyDescriptions(
  companies: FormattedRecommendation[]
): string {
  return companies
    .map(company => {
      // Check if company name is included in description
      const companyNameInDescription = company.translatedDescription.includes(company.name.split(' ')[0]);
      if (companyNameInDescription) {
        return `${company.name}(${company.ticker}) : ${company.translatedDescription}`;
      } else {
        return `${company.name}(${company.ticker})는 ${company.translatedDescription}`;
      }
    })
    .join('\n\n');
}

/**
 * Formats company name with ticker for display
 */
export function formatCompanyDisplay(ticker: string, includeIndustry: boolean = false): string {
  const company = (DATA as any)[ticker];
  if (!company) return ticker;
  
  const base = `${company.name} (${ticker})`;
  return includeIndustry ? `${base} - ${company.industry}` : base;
}

/**
 * Creates a formatted company list for display
 */
export function formatCompanyList(
  tickers: string[], 
  numbered: boolean = true,
  includeIndustry: boolean = false
): string {
  return tickers
    .map((ticker, index) => {
      const prefix = numbered ? `${index + 1}. ` : '• ';
      return prefix + formatCompanyDisplay(ticker, includeIndustry);
    })
    .join('\n');
}

/**
 * Creates a simple ticker list for display
 */
export function formatTickerList(tickers: string[]): string {
  return tickers.join(', ');
}

/**
 * Formats industry information with company count
 */
export function formatIndustryInfo(industry: string): string {
  const companies = getCompaniesByIndustry(industry);
  return `${industry} (${companies.length}개 기업)`;
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Gets a random sample of companies from the entire dataset
 */
export function getRandomCompanySample(count: number = 5): CompanyRecommendation[] {
  const allTickers = Object.keys(DATA);
  const randomTickers = allTickers
    .sort(() => Math.random() - 0.5)
    .slice(0, count);
  
  return randomTickers.map(ticker => {
    const company = (DATA as any)[ticker];
    return {
      ticker,
      name: company.name,
      description: company.description
    };
  });
}

/**
 * Gets companies with similar industry keywords
 */
export function getCompaniesBySimilarIndustry(industryKeyword: string, count: number = 5): CompanyRecommendation[] {
  const keyword = industryKeyword.toLowerCase();
  
  const matchingCompanies = Object.entries(DATA)
    .filter(([_, company]) => {
      const comp = company as any;
      return comp.industry.toLowerCase().includes(keyword);
    })
    .slice(0, count)
    .map(([ticker, company]) => {
      const comp = company as any;
      return {
        ticker,
        name: comp.name,
        description: comp.description
      };
    });
  
  return matchingCompanies;
}

/**
 * Validates recommendation data structure
 */
export function validateRecommendation(recommendation: RandomRecommendation): boolean {
  if (!recommendation.industry || !recommendation.companies) {
    return false;
  }
  
  if (!Array.isArray(recommendation.companies) || recommendation.companies.length === 0) {
    return false;
  }
  
  return recommendation.companies.every(company => 
    company.ticker && company.name && company.description
  );
}
