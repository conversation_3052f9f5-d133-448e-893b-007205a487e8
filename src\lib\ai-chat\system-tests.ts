/**
 * Comprehensive System Tests for Refactored AI Chat System
 * 
 * This module provides comprehensive testing for all refactored components
 * with focus on Korean input handling and error resilience.
 */

import { getEmbeddings } from '@/lib/embeddings';
import { 
  findTickerWithPrecedence, 
  testTickerMatching, 
  validateKoreanMappingCoverage 
} from './ticker-matching';
import { 
  isPositive, 
  isNegative, 
  getResponseType,
  getDetailedResponseClassification 
} from './response-classification';
import { 
  getCompanyData, 
  getAllTickers, 
  getAllIndustries,
  validateDataset,
  getDatasetStats 
} from './company-data';
import { 
  generateRandomRecommendation,
  translateAndFormatRecommendations,
  validateRecommendation 
} from './recommendation-generator';

// ============================================================================
// Test Result Types
// ============================================================================

export interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

export interface SystemTestReport {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: TestResult[];
  summary: string;
}

// ============================================================================
// Core System Tests
// ============================================================================

/**
 * Test embeddings system functionality
 */
export async function testEmbeddingsSystem(): Promise<TestResult> {
  try {
    console.log('🧪 Testing embeddings system...');
    
    const embeddings = await getEmbeddings();
    
    if (!embeddings || !embeddings.companies || !embeddings.industries) {
      throw new Error('Invalid embeddings structure');
    }
    
    if (embeddings.companies.length === 0) {
      throw new Error('No company embeddings found');
    }
    
    if (embeddings.industries.length === 0) {
      throw new Error('No industry embeddings found');
    }
    
    // Test vector dimensions
    const sampleCompany = embeddings.companies[0];
    if (!sampleCompany.vec || sampleCompany.vec.length === 0) {
      throw new Error('Invalid company vector');
    }
    
    const sampleIndustry = embeddings.industries[0];
    if (!sampleIndustry.vec || sampleIndustry.vec.length === 0) {
      throw new Error('Invalid industry vector');
    }
    
    console.log(`✅ Embeddings system test passed: ${embeddings.companies.length} companies, ${embeddings.industries.length} industries`);
    
    return {
      testName: 'Embeddings System',
      passed: true,
      details: {
        companiesCount: embeddings.companies.length,
        industriesCount: embeddings.industries.length,
        vectorDimension: sampleCompany.vec.length
      }
    };
  } catch (error) {
    console.error('❌ Embeddings system test failed:', error);
    return {
      testName: 'Embeddings System',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test ticker matching system with Korean input
 */
export async function testTickerMatchingSystem(): Promise<TestResult> {
  try {
    console.log('🧪 Testing ticker matching system...');
    
    // Run comprehensive ticker matching tests
    const testResults = testTickerMatching();
    
    // Validate Korean mapping coverage
    const coverageResults = validateKoreanMappingCoverage();
    
    const passRate = testResults.passed / (testResults.passed + testResults.failed);
    const coverageRate = coverageResults.validMappings / coverageResults.totalMappings;
    
    if (passRate < 0.8) {
      throw new Error(`Ticker matching pass rate too low: ${(passRate * 100).toFixed(1)}%`);
    }
    
    if (coverageRate < 0.9) {
      throw new Error(`Korean mapping coverage too low: ${(coverageRate * 100).toFixed(1)}%`);
    }
    
    console.log(`✅ Ticker matching system test passed: ${(passRate * 100).toFixed(1)}% pass rate, ${(coverageRate * 100).toFixed(1)}% coverage`);
    
    return {
      testName: 'Ticker Matching System',
      passed: true,
      details: {
        passRate: passRate,
        coverageRate: coverageRate,
        testResults: testResults,
        coverageResults: coverageResults
      }
    };
  } catch (error) {
    console.error('❌ Ticker matching system test failed:', error);
    return {
      testName: 'Ticker Matching System',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test Korean response classification
 */
export async function testResponseClassification(): Promise<TestResult> {
  try {
    console.log('🧪 Testing Korean response classification...');
    
    const testCases = [
      { input: '네', expectedType: 'positive' },
      { input: '좋아요', expectedType: 'positive' },
      { input: '그래요', expectedType: 'positive' },
      { input: '아니요', expectedType: 'negative' },
      { input: '싫어요', expectedType: 'negative' },
      { input: '안돼요', expectedType: 'negative' },
      { input: '모르겠어요', expectedType: 'neutral' },
      { input: '글쎄요', expectedType: 'neutral' }
    ];
    
    let passed = 0;
    const results = [];
    
    for (const testCase of testCases) {
      try {
        const responseType = await getResponseType(testCase.input);
        const isCorrect = responseType === testCase.expectedType;
        
        if (isCorrect) passed++;
        
        results.push({
          input: testCase.input,
          expected: testCase.expectedType,
          actual: responseType,
          passed: isCorrect
        });
      } catch (error) {
        results.push({
          input: testCase.input,
          expected: testCase.expectedType,
          actual: 'error',
          passed: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    const passRate = passed / testCases.length;
    
    if (passRate < 0.7) {
      throw new Error(`Response classification pass rate too low: ${(passRate * 100).toFixed(1)}%`);
    }
    
    console.log(`✅ Response classification test passed: ${(passRate * 100).toFixed(1)}% pass rate`);
    
    return {
      testName: 'Korean Response Classification',
      passed: true,
      details: {
        passRate: passRate,
        results: results
      }
    };
  } catch (error) {
    console.error('❌ Response classification test failed:', error);
    return {
      testName: 'Korean Response Classification',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test company data access and validation
 */
export async function testCompanyDataSystem(): Promise<TestResult> {
  try {
    console.log('🧪 Testing company data system...');
    
    // Test basic data access
    const allTickers = getAllTickers();
    const allIndustries = getAllIndustries();
    
    if (allTickers.length === 0) {
      throw new Error('No tickers found');
    }
    
    if (allIndustries.length === 0) {
      throw new Error('No industries found');
    }
    
    // Test data validation
    const validationResults = validateDataset();
    const validationRate = validationResults.validCompanies / validationResults.totalCompanies;
    
    if (validationRate < 0.95) {
      throw new Error(`Data validation rate too low: ${(validationRate * 100).toFixed(1)}%`);
    }
    
    // Test dataset statistics
    const stats = getDatasetStats();
    
    if (stats.totalCompanies !== allTickers.length) {
      throw new Error('Inconsistent company count');
    }
    
    // Test individual company data access
    const sampleTicker = allTickers[0];
    const companyData = getCompanyData(sampleTicker);
    
    if (!companyData || !companyData.name || !companyData.industry || !companyData.description) {
      throw new Error('Invalid company data structure');
    }
    
    console.log(`✅ Company data system test passed: ${allTickers.length} companies, ${allIndustries.length} industries`);
    
    return {
      testName: 'Company Data System',
      passed: true,
      details: {
        tickersCount: allTickers.length,
        industriesCount: allIndustries.length,
        validationRate: validationRate,
        stats: stats
      }
    };
  } catch (error) {
    console.error('❌ Company data system test failed:', error);
    return {
      testName: 'Company Data System',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test recommendation generation system
 */
export async function testRecommendationSystem(): Promise<TestResult> {
  try {
    console.log('🧪 Testing recommendation generation system...');
    
    // Generate multiple recommendations
    const recommendations = [];
    for (let i = 0; i < 5; i++) {
      const recommendation = generateRandomRecommendation();
      
      if (!validateRecommendation(recommendation)) {
        throw new Error(`Invalid recommendation structure: ${JSON.stringify(recommendation)}`);
      }
      
      recommendations.push(recommendation);
    }
    
    // Test translation and formatting
    const sampleRecommendation = recommendations[0];
    const translatedCompanies = await translateAndFormatRecommendations(sampleRecommendation.companies);
    
    if (translatedCompanies.length !== sampleRecommendation.companies.length) {
      throw new Error('Translation count mismatch');
    }
    
    for (const company of translatedCompanies) {
      if (!company.translatedDescription) {
        throw new Error('Missing translated description');
      }
    }
    
    console.log(`✅ Recommendation system test passed: Generated ${recommendations.length} valid recommendations`);
    
    return {
      testName: 'Recommendation Generation System',
      passed: true,
      details: {
        recommendationsGenerated: recommendations.length,
        sampleRecommendation: sampleRecommendation,
        translationTest: 'passed'
      }
    };
  } catch (error) {
    console.error('❌ Recommendation system test failed:', error);
    return {
      testName: 'Recommendation Generation System',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// ============================================================================
// Comprehensive System Test Runner
// ============================================================================

/**
 * Run all system tests and generate comprehensive report
 */
export async function runComprehensiveSystemTests(): Promise<SystemTestReport> {
  console.log('🚀 Starting comprehensive system tests...');
  console.log('=' .repeat(60));

  const startTime = Date.now();
  const results: TestResult[] = [];

  // Run all tests
  const tests = [
    testEmbeddingsSystem,
    testTickerMatchingSystem,
    testResponseClassification,
    testCompanyDataSystem,
    testRecommendationSystem
  ];

  for (const test of tests) {
    try {
      const result = await test();
      results.push(result);
    } catch (error) {
      results.push({
        testName: test.name,
        passed: false,
        error: error instanceof Error ? error.message : 'Test execution failed'
      });
    }
  }

  const endTime = Date.now();
  const duration = endTime - startTime;

  // Calculate summary
  const passedTests = results.filter(r => r.passed).length;
  const failedTests = results.filter(r => !r.passed).length;
  const totalTests = results.length;

  const summary = generateTestSummary(passedTests, failedTests, totalTests, duration);

  console.log('=' .repeat(60));
  console.log(summary);
  console.log('=' .repeat(60));

  return {
    totalTests,
    passedTests,
    failedTests,
    results,
    summary
  };
}

/**
 * Generate test summary report
 */
function generateTestSummary(passed: number, failed: number, total: number, duration: number): string {
  const passRate = (passed / total * 100).toFixed(1);
  const status = failed === 0 ? '✅ ALL TESTS PASSED' : `❌ ${failed} TEST(S) FAILED`;

  return `
🧪 COMPREHENSIVE SYSTEM TEST REPORT
${status}

📊 Results Summary:
   • Total Tests: ${total}
   • Passed: ${passed} (${passRate}%)
   • Failed: ${failed}
   • Duration: ${duration}ms

🔧 System Status:
   • Embeddings System: ${getTestStatus('Embeddings System', passed, failed)}
   • Ticker Matching: ${getTestStatus('Ticker Matching System', passed, failed)}
   • Response Classification: ${getTestStatus('Korean Response Classification', passed, failed)}
   • Company Data: ${getTestStatus('Company Data System', passed, failed)}
   • Recommendations: ${getTestStatus('Recommendation Generation System', passed, failed)}

${failed === 0 ?
  '🎉 All systems operational! The refactored AI chat system is ready for production.' :
  '⚠️  Some tests failed. Please review the failed tests and fix the issues before deployment.'
}`;
}

/**
 * Get test status for summary
 */
function getTestStatus(testName: string, passed: number, failed: number): string {
  return failed === 0 ? '✅ PASS' : '❌ FAIL';
}

// ============================================================================
// Performance and Load Testing
// ============================================================================

/**
 * Test system performance with multiple concurrent requests
 */
export async function testSystemPerformance(): Promise<TestResult> {
  try {
    console.log('🧪 Testing system performance...');

    const startTime = Date.now();
    const concurrentRequests = 10;
    const testInputs = [
      '애플', '테슬라', '구글', '마이크로소프트', '엔비디아',
      '인텔', '아마존', '메타', '넷플릭스', '퀄컴'
    ];

    // Test concurrent ticker matching
    const tickerPromises = testInputs.map(input =>
      findTickerWithPrecedence(input)
    );

    const tickerResults = await Promise.all(tickerPromises);

    // Test concurrent response classification
    const responsePromises = testInputs.map(input =>
      getResponseType(input)
    );

    const responseResults = await Promise.all(responsePromises);

    const endTime = Date.now();
    const duration = endTime - startTime;
    const avgResponseTime = duration / (concurrentRequests * 2);

    // Validate results
    const validTickerResults = tickerResults.filter(r => r !== null).length;
    const validResponseResults = responseResults.filter(r => r !== null).length;

    if (avgResponseTime > 1000) {
      throw new Error(`Average response time too high: ${avgResponseTime}ms`);
    }

    console.log(`✅ Performance test passed: ${avgResponseTime.toFixed(1)}ms avg response time`);

    return {
      testName: 'System Performance',
      passed: true,
      details: {
        totalDuration: duration,
        avgResponseTime: avgResponseTime,
        concurrentRequests: concurrentRequests * 2,
        validTickerResults: validTickerResults,
        validResponseResults: validResponseResults
      }
    };
  } catch (error) {
    console.error('❌ Performance test failed:', error);
    return {
      testName: 'System Performance',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Quick system health check
 */
export async function quickHealthCheck(): Promise<boolean> {
  try {
    // Test basic functionality
    const tickerResult = findTickerWithPrecedence('애플');
    const responseResult = await getResponseType('네');
    const companyData = getCompanyData('AAPL');
    const recommendation = generateRandomRecommendation();

    return !!(tickerResult && responseResult && companyData && recommendation);
  } catch (error) {
    console.error('Health check failed:', error);
    return false;
  }
}
