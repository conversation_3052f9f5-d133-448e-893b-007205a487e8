import fs from 'fs';
import path from 'path';
import OpenAI from 'openai';
import { QUICK_ENRICHED_FINAL as DATA } from '../data/sp500_enriched_final';
import { getOpenAIClient } from './openai-client';

/* ──────────── 타입 ──────────── */
type Vec = number[];
export interface CompanyRow { ticker: string; name: string; industry: string; vec: Vec; }
export interface IndustryRow { industry: string; vec: Vec; }
interface CacheFile { companies: CompanyRow[]; industries: IndustryRow[]; }

/* ──────────── 상수 ──────────── */
const CACHE_DIR = path.join(process.cwd(), '.cache');
const CACHE = path.join(CACHE_DIR, 'sp500_vectors.json');
const BATCH = 100;

/* ──────────── 벡터 유틸 ──────────── */
export const dot = (a: Vec, b: Vec) => a.reduce((s, x, i) => s + x * b[i], 0);
export const cosine = dot;  // 정규화 후 dot=cos
const norm = (v: Vec) => { const n = Math.hypot(...v); return v.map(x => x / n); };

/* ──────────── 임베딩 생성 ──────────── */
async function createEmbeddings(): Promise<CacheFile> {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [EMBEDDINGS] Starting embedding creation process...`);

  const tickers = Object.keys(DATA);
  const txt = tickers.map(t => `${DATA[t].name}. ${DATA[t].industry}. ${DATA[t].description}`);
  console.log(`[${timestamp}] [EMBEDDINGS] Processing ${tickers.length} companies`);

  /* 기업 496개 */
  const companies: CompanyRow[] = [];
  for (let i = 0; i < txt.length; i += BATCH) {
    console.log(`[${timestamp}] [EMBEDDINGS] Creating embeddings for companies batch ${Math.floor(i/BATCH) + 1}/${Math.ceil(txt.length/BATCH)}`);

    try {
      const { data } = await getOpenAIClient('EMBEDDINGS').embeddings.create({
        model: 'text-embedding-3-small',
        input: txt.slice(i, i + BATCH),
      });

      data.forEach((d, j) => {
        const t = tickers[i + j], b = DATA[t];
        companies.push({ ticker: t, name: b.name, industry: b.industry, vec: norm(d.embedding) });
      });

      console.log(`[${timestamp}] [EMBEDDINGS] ✅ Successfully processed batch ${Math.floor(i/BATCH) + 1}`);
    } catch (error) {
      console.error(`[${timestamp}] [EMBEDDINGS] ❌ Failed to create embeddings for batch ${Math.floor(i/BATCH) + 1}:`, error);
      throw error;
    }
  }

  /* 산업 40개 */
  const inds = [...new Set(companies.map(c => c.industry))];
  console.log(`[${timestamp}] [EMBEDDINGS] Creating embeddings for ${inds.length} industries`);

  try {
    const { data: indEmb } = await getOpenAIClient('EMBEDDINGS').embeddings.create({
      model: 'text-embedding-3-small',
      input: inds.map(s => `${s}: companies in ${s.toLowerCase()}`),
    });
    const industries = indEmb.map((d, i) => ({ industry: inds[i], vec: norm(d.embedding) }));

    console.log(`[${timestamp}] [EMBEDDINGS] ✅ Successfully created industry embeddings`);

    // Ensure cache directory exists
    try {
      fs.mkdirSync(CACHE_DIR, { recursive: true });
      console.log(`[${timestamp}] [EMBEDDINGS] Cache directory ensured: ${CACHE_DIR}`);
    } catch (dirError) {
      console.error(`[${timestamp}] [EMBEDDINGS] Failed to create cache directory:`, dirError);
      throw dirError;
    }

    // Save embeddings to cache file
    try {
      fs.writeFileSync(CACHE, JSON.stringify({ companies, industries }));
      console.log(`[${timestamp}] [EMBEDDINGS] ✅ Embeddings cache saved to ${CACHE}`);
    } catch (writeError) {
      console.error(`[${timestamp}] [EMBEDDINGS] Failed to write cache file:`, writeError);
      // Don't throw here - embeddings are still valid, just not cached
      console.log(`[${timestamp}] [EMBEDDINGS] Continuing without cache file`);
    }

    return { companies, industries };
  } catch (error) {
    console.error(`[${timestamp}] [EMBEDDINGS] ❌ Failed to create industry embeddings:`, error);
    throw error;
  }
}

/* ──────────── 캐시 로드 ──────────── */
let mem: CacheFile | null = null;

export async function getEmbeddings(): Promise<CacheFile> {
  const timestamp = new Date().toISOString();

  if (mem) {
    console.log(`[${timestamp}] [EMBEDDINGS] Using cached embeddings from memory`);
    return mem;
  }

  console.log(`[${timestamp}] [EMBEDDINGS] Loading embeddings...`);
  console.log(`[${timestamp}] [EMBEDDINGS] Cache directory: ${CACHE_DIR}`);
  console.log(`[${timestamp}] [EMBEDDINGS] Cache file path: ${CACHE}`);

  // Check if cache directory exists
  try {
    if (!fs.existsSync(CACHE_DIR)) {
      console.log(`[${timestamp}] [EMBEDDINGS] Cache directory does not exist, will create during embedding generation`);
    }
  } catch (dirCheckError) {
    console.warn(`[${timestamp}] [EMBEDDINGS] Failed to check cache directory:`, dirCheckError);
  }

  // Try to load from cache file
  if (fs.existsSync(CACHE)) {
    try {
      console.log(`[${timestamp}] [EMBEDDINGS] Loading embeddings from cache file`);
      const cacheContent = fs.readFileSync(CACHE, 'utf8');

      if (!cacheContent.trim()) {
        console.warn(`[${timestamp}] [EMBEDDINGS] Cache file is empty, creating new embeddings`);
      } else {
        const cacheData = JSON.parse(cacheContent);

        // Validate cache data structure
        if (cacheData.companies && cacheData.industries &&
            Array.isArray(cacheData.companies) && Array.isArray(cacheData.industries)) {
          mem = cacheData;
          console.log(`[${timestamp}] [EMBEDDINGS] ✅ Successfully loaded ${cacheData.companies.length} companies and ${cacheData.industries.length} industries from cache`);
          return mem;
        } else {
          console.warn(`[${timestamp}] [EMBEDDINGS] Cache file has invalid structure, creating new embeddings`);
        }
      }
    } catch (error) {
      console.error(`[${timestamp}] [EMBEDDINGS] ❌ Failed to load cache file:`, error);
      console.log(`[${timestamp}] [EMBEDDINGS] Falling back to creating new embeddings`);
    }
  } else {
    console.log(`[${timestamp}] [EMBEDDINGS] Cache file does not exist, creating new embeddings`);
  }

  try {
    mem = await createEmbeddings();
    console.log(`[${timestamp}] [EMBEDDINGS] ✅ Successfully created and cached new embeddings`);
    return mem;
  } catch (error) {
    console.error(`[${timestamp}] [EMBEDDINGS] ❌ Failed to create embeddings:`, error);
    throw error;
  }
}
