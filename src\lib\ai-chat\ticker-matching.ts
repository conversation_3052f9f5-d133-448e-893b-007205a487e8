/**
 * Ticker Matching Module
 * 
 * This module provides optimized ticker matching functionality for Korean input.
 * Focused on Korean company name mapping and efficient ticker resolution.
 */

import { QUICK_ENRICHED_FINAL as DATA } from '@/data/sp500_enriched_final';
import { KOREAN_COMPANY_MAPPING } from './config';

// ============================================================================
// Types
// ============================================================================

export interface TickerMatchResult {
  ticker: string;
  name: string;
  industry: string;
  matchType: 'exact_ticker' | 'korean_mapping' | 'exact_company_name' | 'partial_company_name';
}

// ============================================================================
// Optimized Korean Matching Functions
// ============================================================================

interface KoreanMatchResult {
  ticker: string;
  name: string;
  industry: string;
  koreanName: string;
  confidence: number;
}

/**
 * Optimized Korean company name matching with confidence scoring
 */
function findKoreanCompanyMatches(lowerInput: string, tickersToSearch: string[]): KoreanMatchResult[] {
  const matches: KoreanMatchResult[] = [];

  // Pre-process input for better matching
  const processedInput = preprocessKoreanInput(lowerInput);

  for (const [koreanName, englishNames] of Object.entries(KOREAN_COMPANY_MAPPING)) {
    const koreanLower = koreanName.toLowerCase();

    // Calculate match confidence
    let confidence = 0;
    let matchFound = false;

    // Exact Korean name match (highest confidence)
    if (processedInput === koreanLower) {
      confidence = 1.0;
      matchFound = true;
    }
    // Korean name contained in input
    else if (processedInput.includes(koreanLower)) {
      confidence = 0.8;
      matchFound = true;
    }
    // Input contained in Korean name (for shorter inputs)
    else if (koreanLower.includes(processedInput) && processedInput.length >= 2) {
      confidence = 0.6;
      matchFound = true;
    }

    if (matchFound) {
      // Find corresponding ticker
      for (const ticker of tickersToSearch) {
        const company = DATA[ticker as keyof typeof DATA];
        if (!company) continue;

        const companyName = company.name.toLowerCase();
        for (const englishName of englishNames as string[]) {
          if (companyName.includes(englishName.toLowerCase())) {
            matches.push({
              ticker,
              name: company.name,
              industry: company.industry,
              koreanName,
              confidence
            });
            break; // Found match for this Korean name
          }
        }
      }
    }
  }

  // Sort by confidence (highest first)
  return matches.sort((a, b) => b.confidence - a.confidence);
}

/**
 * Preprocesses Korean input for better matching
 */
function preprocessKoreanInput(input: string): string {
  return input
    .toLowerCase()
    .trim()
    // Remove common Korean particles and suffixes
    .replace(/회사$/, '')
    .replace(/기업$/, '')
    .replace(/그룹$/, '')
    .replace(/코퍼레이션$/, '')
    .replace(/코프$/, '')
    .replace(/주식회사$/, '')
    .replace(/\s+/g, ''); // Remove spaces for better matching
}

// ============================================================================
// Core Ticker Matching Functions
// ============================================================================

/**
 * Optimized ticker matching function for Korean input with enhanced performance
 *
 * Precedence Order:
 * 1. Exact ticker match (TSLA -> Tesla, Inc.)
 * 2. Korean company name mapping (optimized with pre-processing)
 * 3. English company name exact match
 * 4. English company name partial match (with strict rules)
 */
export function findTickerWithPrecedence(
  input: string,
  availableTickers?: string[]
): TickerMatchResult | null {
  const normalizedInput = input.trim();
  const upperInput = normalizedInput.toUpperCase();
  const lowerInput = normalizedInput.toLowerCase();

  // Use all tickers if no specific list provided
  const tickersToSearch = availableTickers || Object.keys(DATA);

  console.log(`🔍 Optimized ticker search for: "${input}" in ${tickersToSearch.length} tickers`);

  // PRIORITY 1: Exact ticker match (highest priority) - O(1) lookup
  if (tickersToSearch.includes(upperInput)) {
    const company = DATA[upperInput as keyof typeof DATA];
    if (company) {
      console.log(`🎯 PRIORITY 1 - Exact ticker match: ${input} -> ${upperInput} (${company.name})`);
      return {
        ticker: upperInput,
        name: company.name,
        industry: company.industry,
        matchType: 'exact_ticker'
      };
    }
  }

  // PRIORITY 2: Korean company name mapping (optimized)
  const koreanMatches = findKoreanCompanyMatches(lowerInput, tickersToSearch);
  if (koreanMatches.length > 0) {
    const bestMatch = koreanMatches[0]; // Take the first (best) match
    console.log(`🎯 PRIORITY 2 - Korean name match: "${bestMatch.koreanName}" -> ${bestMatch.ticker} (${bestMatch.name})`);
    return {
      ticker: bestMatch.ticker,
      name: bestMatch.name,
      industry: bestMatch.industry,
      matchType: 'korean_mapping'
    };
  }

  // PRIORITY 3: English company name exact match
  for (const ticker of tickersToSearch) {
    const company = DATA[ticker as keyof typeof DATA];
    if (!company) continue;

    const companyName = company.name.toLowerCase();

    // Exact company name match (both directions)
    if (companyName === lowerInput || lowerInput === companyName) {
      console.log(`🎯 PRIORITY 3 - Exact company name match: "${input}" -> ${ticker} (${company.name})`);
      return {
        ticker,
        name: company.name,
        industry: company.industry,
        matchType: 'exact_company_name'
      };
    }
  }

  // PRIORITY 4: English company name partial match (with strict rules)
  for (const ticker of tickersToSearch) {
    const company = DATA[ticker as keyof typeof DATA];
    if (!company) continue;

    const companyName = company.name.toLowerCase();

    // Partial match with strict rules (minimum 4 characters, avoid common words)
    if (lowerInput.length >= 4 && 
        !['corp', 'inc', 'ltd', 'company', 'group'].includes(lowerInput) &&
        companyName.includes(lowerInput)) {
      console.log(`🎯 PRIORITY 4 - Partial company name match: "${input}" -> ${ticker} (${company.name})`);
      return {
        ticker,
        name: company.name,
        industry: company.industry,
        matchType: 'partial_company_name'
      };
    }
  }

  console.log(`❌ No ticker match found for: "${input}"`);
  return null;
}

/**
 * Quick ticker validation
 */
export function isValidTicker(ticker: string): boolean {
  return ticker in DATA;
}

/**
 * Get company name by ticker
 */
export function getCompanyName(ticker: string): string {
  const company = (DATA as any)[ticker];
  return company ? company.name : ticker;
}

/**
 * Direct ticker match for exact ticker symbols
 */
export function getDirectTickerMatch(input: string): string | null {
  const upperInput = input.trim().toUpperCase();
  return isValidTicker(upperInput) ? upperInput : null;
}

/**
 * Find ticker in text using Korean company mapping (optimized)
 */
export function findTickerInKoreanText(text: string): string | null {
  const result = findTickerWithPrecedence(text);
  return result ? result.ticker : null;
}

// ============================================================================
// Testing and Validation Functions
// ============================================================================

/**
 * Test the ticker matching system with common Korean inputs
 */
export function testTickerMatching(): {
  passed: number;
  failed: number;
  results: Array<{input: string, expected: string, actual: string | null, passed: boolean}>;
} {
  const testCases = [
    // Exact ticker matches
    { input: 'AAPL', expected: 'AAPL' },
    { input: 'TSLA', expected: 'TSLA' },
    { input: 'MSFT', expected: 'MSFT' },

    // Korean company names
    { input: '애플', expected: 'AAPL' },
    { input: '테슬라', expected: 'TSLA' },
    { input: '마이크로소프트', expected: 'MSFT' },
    { input: '마소', expected: 'MSFT' },
    { input: '구글', expected: 'GOOGL' },
    { input: '알파벳', expected: 'GOOGL' },
    { input: '엔비디아', expected: 'NVDA' },
    { input: '인텔', expected: 'INTC' },
    { input: '아마존', expected: 'AMZN' },
    { input: '메타', expected: 'META' },
    { input: '페이스북', expected: 'META' },
    { input: '넷플릭스', expected: 'NFLX' },

    // Korean with company suffixes
    { input: '애플 회사', expected: 'AAPL' },
    { input: '테슬라 기업', expected: 'TSLA' },
    { input: '구글 그룹', expected: 'GOOGL' },

    // Partial Korean names
    { input: '애', expected: null }, // Too short
    { input: '테슬', expected: 'TSLA' },
    { input: '마이크로', expected: 'MSFT' },
  ];

  const results = testCases.map(testCase => {
    const result = findTickerWithPrecedence(testCase.input);
    const actual = result ? result.ticker : null;
    const passed = actual === testCase.expected;

    return {
      input: testCase.input,
      expected: testCase.expected,
      actual,
      passed
    };
  });

  const passed = results.filter(r => r.passed).length;
  const failed = results.filter(r => !r.passed).length;

  console.log(`🧪 Ticker Matching Test Results: ${passed}/${results.length} passed`);

  // Log failed tests
  results.filter(r => !r.passed).forEach(result => {
    console.log(`❌ Failed: "${result.input}" -> expected: ${result.expected}, got: ${result.actual}`);
  });

  return { passed, failed, results };
}

/**
 * Validate Korean company mapping coverage
 */
export function validateKoreanMappingCoverage(): {
  totalMappings: number;
  validMappings: number;
  invalidMappings: string[];
} {
  const invalidMappings: string[] = [];
  let validMappings = 0;

  for (const [koreanName, englishNames] of Object.entries(KOREAN_COMPANY_MAPPING)) {
    let found = false;

    for (const englishName of englishNames) {
      // Check if any ticker matches this English name
      for (const ticker of Object.keys(DATA)) {
        const company = DATA[ticker as keyof typeof DATA];
        if (company && company.name.toLowerCase().includes(englishName.toLowerCase())) {
          found = true;
          break;
        }
      }
      if (found) break;
    }

    if (found) {
      validMappings++;
    } else {
      invalidMappings.push(koreanName);
    }
  }

  const totalMappings = Object.keys(KOREAN_COMPANY_MAPPING).length;

  console.log(`📊 Korean Mapping Coverage: ${validMappings}/${totalMappings} valid mappings`);

  if (invalidMappings.length > 0) {
    console.log(`⚠️ Invalid mappings found:`, invalidMappings);
  }

  return {
    totalMappings,
    validMappings,
    invalidMappings
  };
}
