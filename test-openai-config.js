/**
 * Test script to verify OpenAI API key configuration is working
 */

const http = require('http');

async function testOpenAIConfig() {
  console.log('🧪 Testing OpenAI API Key Configuration...\n');
  
  const data = JSON.stringify({
    message: "안녕하세요"
  });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/ai_chat',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      console.log(`📊 Response Status: ${res.statusCode}`);
      console.log(`📋 Response Headers:`, res.headers);
      
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        console.log('\n📄 Raw Response Body:', body);
        
        try {
          const parsed = JSON.parse(body);
          console.log('\n✅ Parsed Response:', JSON.stringify(parsed, null, 2));
          
          if (res.statusCode === 200 && parsed.reply) {
            console.log('\n🎉 SUCCESS: OpenAI API key configuration is working!');
            console.log(`🤖 AI Response: "${parsed.reply.substring(0, 100)}..."`);
            resolve(true);
          } else if (res.statusCode === 500 && parsed.error?.includes('OpenAI')) {
            console.log('\n❌ FAILED: OpenAI API key configuration error detected');
            console.log(`🚨 Error: ${parsed.error}`);
            console.log(`📝 Details: ${parsed.details || 'No details provided'}`);
            resolve(false);
          } else {
            console.log('\n⚠️  UNEXPECTED: Unexpected response format');
            console.log(`📊 Status: ${res.statusCode}`);
            console.log(`📄 Response: ${JSON.stringify(parsed, null, 2)}`);
            resolve(false);
          }
        } catch (parseError) {
          console.log('\n❌ FAILED: Could not parse JSON response');
          console.log(`🚨 Parse Error: ${parseError.message}`);
          console.log(`📄 Raw Body: ${body}`);
          resolve(false);
        }
      });
    });

    req.on('error', (e) => {
      console.log(`\n❌ FAILED: Network error - ${e.message}`);
      console.log('🔍 Make sure the development server is running on http://localhost:3000');
      reject(e);
    });

    req.write(data);
    req.end();
  });
}

async function runTest() {
  try {
    const result = await testOpenAIConfig();
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST RESULT SUMMARY');
    console.log('='.repeat(60));
    
    if (result) {
      console.log('✅ OpenAI API Key Configuration: WORKING');
      console.log('🎯 Status: All environment variables are properly loaded');
      console.log('🚀 Recommendation: The application is ready for use');
    } else {
      console.log('❌ OpenAI API Key Configuration: FAILED');
      console.log('🔧 Action Required: Check environment variables and server logs');
      console.log('📋 Next Steps:');
      console.log('   1. Verify .env.local file exists and contains OPENAI_API_KEY');
      console.log('   2. Check server console for detailed error messages');
      console.log('   3. Restart the development server');
    }
    
    console.log('='.repeat(60));
    
  } catch (error) {
    console.log('\n❌ Test execution failed:', error.message);
  }
}

// Run the test
runTest();
