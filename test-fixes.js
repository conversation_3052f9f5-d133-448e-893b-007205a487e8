/**
 * Test script to verify both fixes are working
 * 1. OpenAI API Key Configuration
 * 2. Chat Flow Logic Bug
 */

const fetch = require('node-fetch');

const API_URL = 'http://localhost:3000/api/ai_chat';

async function testAPIKeyFix() {
  console.log('🔧 Testing OpenAI API Key Fix...');
  
  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: '안녕하세요'
      })
    });

    const data = await response.json();
    
    if (response.ok && data.reply) {
      console.log('✅ OpenAI API Key Fix: SUCCESS');
      console.log('   Response:', data.reply.substring(0, 50) + '...');
      return true;
    } else {
      console.log('❌ OpenAI API Key Fix: FAILED');
      console.log('   Error:', data);
      return false;
    }
  } catch (error) {
    console.log('❌ OpenAI API Key Fix: FAILED');
    console.log('   Error:', error.message);
    return false;
  }
}

async function testChatFlowFix() {
  console.log('\n🔧 Testing Chat Flow Logic Fix...');
  
  try {
    // Step 1: Start conversation with a company name
    console.log('Step 1: Asking about Tesla...');
    const response1 = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: '테슬라'
      })
    });

    const data1 = await response1.json();
    console.log('Response 1:', data1.reply.substring(0, 100) + '...');

    // Step 2: Respond with "네" (Yes)
    console.log('\nStep 2: Responding with "네" (Yes)...');
    const response2 = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': response1.headers.get('set-cookie') || ''
      },
      body: JSON.stringify({
        message: '네'
      })
    });

    const data2 = await response2.json();
    console.log('Response 2:', data2.reply.substring(0, 100) + '...');

    // Check if the response indicates chart/analysis (not casual chat)
    const isChartResponse = data2.reply.includes('차트') || 
                           data2.reply.includes('분석') || 
                           data2.reply.includes('SpeedTraffic') ||
                           data2.status === 'chart_requested';

    if (isChartResponse) {
      console.log('✅ Chat Flow Logic Fix: SUCCESS');
      console.log('   Correctly routed to chart/analysis flow');
      return true;
    } else {
      console.log('❌ Chat Flow Logic Fix: FAILED');
      console.log('   Still routing to casual chat instead of chart flow');
      return false;
    }

  } catch (error) {
    console.log('❌ Chat Flow Logic Fix: FAILED');
    console.log('   Error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🧪 Running Fix Verification Tests...\n');
  
  const apiKeyResult = await testAPIKeyFix();
  const chatFlowResult = await testChatFlowFix();
  
  console.log('\n📊 Test Results Summary:');
  console.log(`OpenAI API Key Fix: ${apiKeyResult ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Chat Flow Logic Fix: ${chatFlowResult ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (apiKeyResult && chatFlowResult) {
    console.log('\n🎉 All fixes are working correctly!');
  } else {
    console.log('\n⚠️  Some fixes need additional work.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testAPIKeyFix, testChatFlowFix, runTests };
