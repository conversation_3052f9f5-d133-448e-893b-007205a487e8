/**
 * AI Service Integration Module
 * 
 * This module handles all OpenAI API interactions including:
 * - OpenAI client initialization and configuration
 * - Intent classification using pattern matching and GPT
 * - Persona-based response generation
 * - Translation services
 * - GPT-based industry classification
 */

import OpenAI from 'openai';
import { IntentClassificationResult, PersonaContext, AIServiceError } from './types';
import { OPENAI_CONFIG, PATTERNS, FALLBACK_RESPONSES, KOREAN_COMPANY_MAPPING, ENV_CONFIG } from './config';
import { getOpenAIClient } from '../openai-client';

// ============================================================================
// OpenAI Client Initialization
// ============================================================================

// OpenAI client is now managed by the centralized client factory

// ============================================================================
// JSON Parsing Utilities
// ============================================================================

/**
 * Cleans and validates JSON response from GPT to prevent parsing errors
 * Enhanced to handle the specific error at position 4 (line 2 column 3)
 */
function cleanJsonResponse(response: string): string {
  // Remove any leading/trailing whitespace
  let cleaned = response.trim();

  // Remove any markdown code block markers
  cleaned = cleaned.replace(/^```json\s*/, '').replace(/\s*```$/, '');
  cleaned = cleaned.replace(/^```\s*/, '').replace(/\s*```$/, '');

  // Handle the specific error at position 4 - check for malformed JSON start
  if (cleaned.length > 4) {
    const firstFourChars = cleaned.substring(0, 4);
    console.log(`[JSON_CLEAN] First 4 characters: "${firstFourChars}"`);

    // Check for common malformations at the beginning
    if (firstFourChars.includes('\n') || firstFourChars.includes('\r')) {
      // Remove newlines at the beginning
      cleaned = cleaned.replace(/^[\r\n\s]+/, '');
      console.log(`[JSON_CLEAN] Removed leading newlines`);
    }

    // Check for invalid characters at position 4
    if (!cleaned.startsWith('{')) {
      // Try to find the actual JSON start
      const jsonStartIndex = cleaned.indexOf('{');
      if (jsonStartIndex > 0) {
        cleaned = cleaned.substring(jsonStartIndex);
        console.log(`[JSON_CLEAN] Found JSON start at position ${jsonStartIndex}`);
      }
    }
  }

  // Ensure the response starts with a valid JSON object
  if (!cleaned.startsWith('{')) {
    console.log(`[JSON_CLEAN] Response doesn't start with '{', attempting to wrap`);
    // Try to extract JSON-like content and wrap it
    const jsonMatch = cleaned.match(/.*?(\{.*\})/s);
    if (jsonMatch) {
      cleaned = jsonMatch[1];
    } else {
      // Last resort: create a minimal valid JSON
      return '{"intent":"casual_chat","confidence":0.3,"reasoning":"Malformed response"}';
    }
  }

  // Handle truncated strings by closing them properly
  if (cleaned.includes('"reasoning"')) {
    // Check for incomplete reasoning field
    const reasoningMatch = cleaned.match(/"reasoning"\s*:\s*"([^"]*?)$/);
    if (reasoningMatch) {
      cleaned = cleaned.replace(/"reasoning"\s*:\s*"([^"]*?)$/, '"reasoning":"$1"');
      console.log(`[JSON_CLEAN] Fixed truncated reasoning field`);
    }
  }

  // Ensure the JSON object is properly closed
  if (!cleaned.endsWith('}')) {
    cleaned += '}';
    console.log(`[JSON_CLEAN] Added missing closing brace`);
  }

  // Validate the cleaned JSON
  try {
    JSON.parse(cleaned);
    console.log(`[JSON_CLEAN] Successfully cleaned JSON`);
    return cleaned;
  } catch (error) {
    console.log(`[JSON_CLEAN] Still invalid after cleaning, using fallback extraction`);
    // If still invalid, try to extract just the complete parts
    const intentMatch = cleaned.match(/"intent"\s*:\s*"([^"]+)"/);
    const confidenceMatch = cleaned.match(/"confidence"\s*:\s*([0-9.]+)/);

    if (intentMatch && confidenceMatch) {
      const fallbackJson = `{"intent":"${intentMatch[1]}","confidence":${confidenceMatch[1]},"reasoning":"Fallback extraction"}`;
      console.log(`[JSON_CLEAN] Created fallback JSON: ${fallbackJson}`);
      return fallbackJson;
    }

    // Ultimate fallback
    console.log(`[JSON_CLEAN] Using ultimate fallback JSON`);
    return '{"intent":"casual_chat","confidence":0.3,"reasoning":"Complete parsing failure"}';
  }
}

/**
 * Extracts JSON values using fallback regex patterns
 */
function extractJsonValuesWithFallback(response: string): {
  intent: string;
  confidence: number;
  reasoning?: string;
} {
  // Enhanced regex patterns for more robust extraction
  const intentPatterns = [
    /"intent"\s*:\s*"([^"]+)"/,
    /'intent'\s*:\s*'([^']+)'/,
    /intent\s*:\s*"([^"]+)"/,
    /intent\s*:\s*'([^']+)'/
  ];

  const confidencePatterns = [
    /"confidence"\s*:\s*([0-9.]+)/,
    /'confidence'\s*:\s*([0-9.]+)/,
    /confidence\s*:\s*([0-9.]+)/
  ];

  const reasoningPatterns = [
    /"reasoning"\s*:\s*"([^"]*?)"/,
    /'reasoning'\s*:\s*'([^']*?)'/,
    /reasoning\s*:\s*"([^"]*?)"/
  ];

  let extractedIntent = 'casual_chat';
  let extractedConfidence = 0.3;
  let extractedReasoning = 'Fallback extraction';

  // Try to extract intent
  for (const pattern of intentPatterns) {
    const match = response.match(pattern);
    if (match) {
      extractedIntent = match[1];
      break;
    }
  }

  // Try to extract confidence
  for (const pattern of confidencePatterns) {
    const match = response.match(pattern);
    if (match) {
      extractedConfidence = parseFloat(match[1]);
      break;
    }
  }

  // Try to extract reasoning
  for (const pattern of reasoningPatterns) {
    const match = response.match(pattern);
    if (match) {
      extractedReasoning = match[1];
      break;
    }
  }

  return {
    intent: extractedIntent,
    confidence: Math.max(0, Math.min(1, extractedConfidence)),
    reasoning: extractedReasoning
  };
}

/**
 * Extracts company context values using fallback regex patterns
 */
function extractCompanyContextWithFallback(response: string): {
  isInvestmentRelated: boolean;
  confidence: number;
  reasoning: string;
} {
  // Enhanced regex patterns for company context extraction
  const investmentPatterns = [
    /"isInvestmentRelated"\s*:\s*(true|false)/,
    /'isInvestmentRelated'\s*:\s*(true|false)/,
    /isInvestmentRelated\s*:\s*(true|false)/
  ];

  const confidencePatterns = [
    /"confidence"\s*:\s*([0-9.]+)/,
    /'confidence'\s*:\s*([0-9.]+)/,
    /confidence\s*:\s*([0-9.]+)/
  ];

  const reasoningPatterns = [
    /"reasoning"\s*:\s*"([^"]*?)"/,
    /'reasoning'\s*:\s*'([^']*?)'/,
    /reasoning\s*:\s*"([^"]*?)"/
  ];

  let extractedInvestment = true; // Default to investment-related
  let extractedConfidence = 0.5;
  let extractedReasoning = 'Fallback extraction';

  // Try to extract investment flag
  for (const pattern of investmentPatterns) {
    const match = response.match(pattern);
    if (match) {
      extractedInvestment = match[1] === 'true';
      break;
    }
  }

  // Try to extract confidence
  for (const pattern of confidencePatterns) {
    const match = response.match(pattern);
    if (match) {
      extractedConfidence = parseFloat(match[1]);
      break;
    }
  }

  // Try to extract reasoning
  for (const pattern of reasoningPatterns) {
    const match = response.match(pattern);
    if (match) {
      extractedReasoning = match[1];
      break;
    }
  }

  return {
    isInvestmentRelated: extractedInvestment,
    confidence: Math.max(0, Math.min(1, extractedConfidence)),
    reasoning: extractedReasoning
  };
}

/**
 * Test function for JSON parsing robustness
 * Tests various malformed JSON scenarios to ensure bulletproof parsing
 */
export function testJsonParsing(): void {
  const testCases = [
    // Test case 1: Position 4 error scenario
    '{\n  "intent": "test"',
    // Test case 2: Truncated reasoning
    '{"intent":"test","confidence":0.8,"reasoning":"incomplete',
    // Test case 3: Missing closing brace
    '{"intent":"test","confidence":0.8,"reasoning":"complete"',
    // Test case 4: Extra characters at start
    'Some text\n{"intent":"test","confidence":0.8,"reasoning":"test"}',
    // Test case 5: Markdown wrapped
    '```json\n{"intent":"test","confidence":0.8,"reasoning":"test"}\n```',
    // Test case 6: Empty response
    '',
    // Test case 7: Non-JSON response
    'This is not JSON at all',
    // Test case 8: Valid JSON
    '{"intent":"test","confidence":0.8,"reasoning":"test"}'
  ];

  console.log('🧪 Testing JSON parsing robustness...');

  testCases.forEach((testCase, index) => {
    try {
      const cleaned = cleanJsonResponse(testCase);
      const parsed = JSON.parse(cleaned);
      console.log(`✅ Test ${index + 1} passed: ${JSON.stringify(parsed)}`);
    } catch (error) {
      console.log(`❌ Test ${index + 1} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  });

  console.log('🧪 JSON parsing robustness test completed');
}

// ============================================================================
// Intent Classification
// ============================================================================

/**
 * Classifies user intent using GPT-4.1-nano based analysis
 * This replaces all hardcoded pattern matching with AI-based classification
 */
export async function classifyUserIntent(userInput: string): Promise<IntentClassificationResult> {
  try {
    // First check for direct company name mentions (performance optimization)
    const lowerInput = userInput.toLowerCase().trim();
    for (const koreanName of Object.keys(KOREAN_COMPANY_MAPPING)) {
      if (lowerInput.includes(koreanName)) {
        // Use GPT to determine if this is investment-related company mention
        const companyContextResult = await classifyCompanyMentionContext(userInput, koreanName);
        if (companyContextResult.isInvestmentRelated) {
          console.log(`✅ Company direct match with investment context: ${koreanName}`);
          return {
            intent: 'company_direct',
            confidence: companyContextResult.confidence,
            reasoning: `기업명 직접 언급 (${koreanName}) - GPT 분석 결과: ${companyContextResult.reasoning}`
          };
        }
      }
    }

    // Use GPT-4.1-nano for comprehensive intent classification
    const gptResult = await classifyIntentWithGPT(userInput);
    console.log(`🤖 GPT Intent: ${gptResult.intent} (${(gptResult.confidence * 100).toFixed(0)}%)`);

    return gptResult;

  } catch (error) {
    console.error('GPT intent classification failed, using fallback:', error);

    // Fallback to basic classification if GPT fails
    return {
      intent: 'casual_chat',
      confidence: 0.3,
      reasoning: `GPT 분류 실패로 인한 기본 분류: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

// ============================================================================
// GPT-based Intent Classification System
// ============================================================================

/**
 * Classifies user intent using GPT-4.1-nano
 * Replaces all hardcoded pattern matching with AI-based analysis
 */
async function classifyIntentWithGPT(userInput: string): Promise<IntentClassificationResult> {
  const prompt = `다음 사용자 입력을 분석하여 의도를 분류해주세요.

사용자 입력: "${userInput}"

분류 카테고리:
1. greeting - 인사말 (안녕하세요, 하이, hello 등)
2. about_ai - AI에 대한 질문 (누구야, 몇 살이야, 뭐 할 수 있어 등)
3. positive_response - 긍정적 응답 (네, 예, 응, 좋아, 그래, yes, ok 등)
4. negative_response - 부정적 응답 (아니, 아니요, 싫어, 안돼, no 등)
5. casual_chat - 일반 대화 (날씨, 음식, 일상 대화 등)
6. investment_query - 투자/산업 관련 질문 (반도체, 전기차, 바이오 등)
7. investment_recommendation - 투자 추천 요청 (투자 추천해줘, 어떤 기업이 좋을까 등)
8. company_direct - 특정 기업명 언급 (이미 처리됨, 이 경우는 드물음)

응답 형식 (JSON):
{
  "intent": "카테고리명",
  "confidence": 0.0-1.0,
  "reasoning": "간단한 분류 이유 (20자 이내)"
}

중요 사항:
- 단순한 "네", "아니오" 같은 응답은 positive_response/negative_response로 분류
- 투자나 금융과 관련 없는 일반 대화는 casual_chat
- 투자 관련 키워드가 있으면 investment_query
- AI 자신에 대한 질문은 about_ai
- 신뢰도는 분류의 확실성을 나타냄 (0.0-1.0)
- 반드시 완전한 JSON 형식으로만 응답하세요
- reasoning은 20자 이내로 간단하게 작성하세요`;

  const response = await getOpenAIClient('AI_SERVICE').chat.completions.create({
    model: OPENAI_CONFIG.model,
    messages: [
      {
        role: 'system',
        content: '당신은 사용자 의도 분류 전문가입니다. 주어진 입력을 정확하게 분류하고 완전한 JSON 형식으로만 응답하세요. 절대 다른 텍스트를 포함하지 마세요.'
      },
      {
        role: 'user',
        content: prompt
      }
    ],
    temperature: OPENAI_CONFIG.temperature.classification,
    max_tokens: OPENAI_CONFIG.maxTokens.classification,
  });

  const aiResponse = response.choices[0].message.content?.trim();

  if (!aiResponse) {
    throw new AIServiceError('GPT intent classification returned empty response');
  }

  try {
    // Clean and validate the response before parsing
    const cleanedResponse = cleanJsonResponse(aiResponse);

    // Additional validation before JSON.parse to prevent position 4 error
    if (!cleanedResponse || cleanedResponse.length < 5) {
      throw new Error('Response too short to be valid JSON');
    }

    if (!cleanedResponse.startsWith('{') || !cleanedResponse.endsWith('}')) {
      throw new Error('Response does not appear to be a valid JSON object');
    }

    console.log(`[JSON_PARSE] Attempting to parse cleaned response: ${cleanedResponse.substring(0, 50)}...`);

    // First attempt: direct JSON parsing
    const parsed = JSON.parse(cleanedResponse);

    // Validate the response structure
    if (!parsed.intent || typeof parsed.confidence !== 'number') {
      throw new Error('Invalid response structure from GPT');
    }

    // Ensure confidence is within valid range
    const confidence = Math.max(0, Math.min(1, parsed.confidence));

    return {
      intent: parsed.intent,
      confidence,
      reasoning: `GPT 분석: ${parsed.reasoning || 'No reasoning provided'}`
    };

  } catch (parseError) {
    console.error('Failed to parse GPT intent classification JSON:', aiResponse);
    console.error('Parse error:', parseError);

    // Fallback: try to extract values using robust regex patterns
    try {
      const fallbackResult = extractJsonValuesWithFallback(aiResponse);

      console.log(`Fallback parsing: intent=${fallbackResult.intent} (${(fallbackResult.confidence * 100).toFixed(0)}%)`);

      return {
        intent: fallbackResult.intent,
        confidence: fallbackResult.confidence,
        reasoning: fallbackResult.reasoning || 'Fallback parsing due to malformed JSON'
      };
    } catch (fallbackError) {
      console.error('Fallback parsing also failed:', fallbackError);

      // Final fallback: return casual_chat with low confidence
      return {
        intent: 'casual_chat',
        confidence: 0.3,
        reasoning: 'JSON parsing failed, using default casual_chat classification'
      };
    }
  }
}

/**
 * Analyzes company mention context using GPT
 */
async function classifyCompanyMentionContext(userInput: string, companyName: string): Promise<{
  isInvestmentRelated: boolean;
  confidence: number;
  reasoning: string;
}> {
  const prompt = `사용자가 "${companyName}"라는 기업을 언급했습니다.

사용자 입력: "${userInput}"

이 입력이 투자/금융과 관련된 맥락인지 판단해주세요.

투자 관련 맥락의 예:
- 주식, 투자, 분석, 차트, 매수, 매도, 추천, 전망
- 기업 분석, 재무 상태, 수익성
- 단순히 기업명만 언급 (투자 목적으로 추정)

투자와 무관한 맥락의 예:
- 제품 사용 경험, 서비스 문의
- 뉴스나 일반적인 이야기
- 취업이나 채용 관련

응답 형식 (JSON):
{
  "isInvestmentRelated": true/false,
  "confidence": 0.0-1.0,
  "reasoning": "간단한 판단 이유 (20자 이내)"
}`;

  const response = await getOpenAIClient('AI_SERVICE').chat.completions.create({
    model: OPENAI_CONFIG.model,
    messages: [
      {
        role: 'system',
        content: '당신은 투자 맥락 분석 전문가입니다. 기업명 언급이 투자 관련인지 정확하게 판단하고 완전한 JSON 형식으로만 응답하세요.'
      },
      {
        role: 'user',
        content: prompt
      }
    ],
    temperature: OPENAI_CONFIG.temperature.classification,
    max_tokens: OPENAI_CONFIG.maxTokens.classification,
  });

  const aiResponse = response.choices[0].message.content?.trim();

  if (!aiResponse) {
    // Default to investment-related if GPT fails
    return {
      isInvestmentRelated: true,
      confidence: 0.5,
      reasoning: 'GPT 응답 실패로 인한 기본 투자 관련 분류'
    };
  }

  try {
    // Clean and validate the response before parsing
    const cleanedResponse = cleanJsonResponse(aiResponse);

    // Additional validation before JSON.parse to prevent position 4 error
    if (!cleanedResponse || cleanedResponse.length < 5) {
      throw new Error('Response too short to be valid JSON');
    }

    if (!cleanedResponse.startsWith('{') || !cleanedResponse.endsWith('}')) {
      throw new Error('Response does not appear to be a valid JSON object');
    }

    console.log(`[JSON_PARSE] Attempting to parse company context response: ${cleanedResponse.substring(0, 50)}...`);

    // First attempt: direct JSON parsing
    const parsed = JSON.parse(cleanedResponse);
    return {
      isInvestmentRelated: parsed.isInvestmentRelated || false,
      confidence: Math.max(0, Math.min(1, parsed.confidence || 0.5)),
      reasoning: parsed.reasoning || 'GPT 분석 결과'
    };
  } catch (parseError) {
    console.error('Failed to parse company context JSON:', aiResponse);
    console.error('Parse error:', parseError);

    // Fallback: try to extract values using robust regex patterns
    try {
      const fallbackResult = extractCompanyContextWithFallback(aiResponse);

      console.log(`Company context fallback: investment=${fallbackResult.isInvestmentRelated} (${(fallbackResult.confidence * 100).toFixed(0)}%)`);

      return fallbackResult;

    } catch (fallbackError) {
      console.error('Fallback parsing also failed:', fallbackError);

      // Default to investment-related if all parsing fails
      return {
        isInvestmentRelated: true,
        confidence: 0.5,
        reasoning: 'JSON parsing failed, defaulting to investment-related'
      };
    }
  }
}

// ============================================================================
// GPT-based Industry Classification
// ============================================================================

/**
 * Uses GPT to classify industry when RAG performance is low
 */
export async function classifyIndustryWithGPT(userInput: string, availableIndustries: string[]): Promise<string | null> {
  try {
    const prompt = `다음 사용자 입력을 분석하여 가장 적합한 산업군을 선택해주세요.

사용자 입력: "${userInput}"

사용 가능한 산업군 목록:
${availableIndustries.map((industry: string, index: number) => `${index + 1}. ${industry}`).join('\n')}

매핑 가이드:
- "그래픽카드", "GPU", "칩", "반도체" → "Semiconductors & Foundries"
- "미디어", "엔터테인먼트" → "Media & Entertainment"
- "바이오", "제약" → "Biotechnology" 또는 "Pharmaceuticals"
- "클라우드", "IT" → "Cloud & IT Services"
- "소프트웨어" → "Application Software"

규칙:
1. 위 목록에서만 선택해야 합니다
2. 가장 관련성이 높은 산업군 1개만 반환하세요
3. 산업군 이름을 정확히 반환하세요 (번호나 다른 텍스트 없이)
4. 확신이 없으면 가장 가까운 산업군을 선택하세요

예시:
- "반도체" → "Semiconductors & Foundries"
- "그래픽카드" → "Semiconductors & Foundries"
- "은행" → "Banks"
- "전기차" → "Automobiles & Components"
- "클라우드" → "Cloud & IT Services"`;

    const response = await getOpenAIClient('AI_SERVICE').chat.completions.create({
      model: OPENAI_CONFIG.model,
      messages: [
        { role: 'system', content: '당신은 산업 분류 전문가입니다. 주어진 목록에서만 정확한 산업군을 선택해주세요.' },
        { role: 'user', content: prompt }
      ],
      temperature: OPENAI_CONFIG.temperature.classification,
      max_tokens: OPENAI_CONFIG.maxTokens.classification,
    });

    const selectedIndustry = response.choices[0].message.content?.trim();

    // Validate that the selected industry is in the available list
    if (selectedIndustry && availableIndustries.includes(selectedIndustry)) {
      console.log(`GPT classification: "${userInput}" → "${selectedIndustry}"`);
      return selectedIndustry;
    } else {
      console.log(`GPT returned invalid industry: "${selectedIndustry}"`);
      return null;
    }
  } catch (error) {
    console.error('GPT classification failed:', error);
    throw new AIServiceError(`GPT classification failed: ${error}`);
  }
}

// ============================================================================
// Translation Services
// ============================================================================



/**
 * Translates English company description to Korean
 */
export async function translateDescription(description: string): Promise<string> {
  try {
    const response = await getOpenAIClient('AI_SERVICE').chat.completions.create({
      model: OPENAI_CONFIG.model,
      messages: [
        {
          role: 'system',
          content: '영어 기업 설명을 자연스러운 한국어로 번역해주세요. 간결하고 이해하기 쉽게 번역하세요.'
        },
        {
          role: 'user',
          content: description
        }
      ],
      temperature: OPENAI_CONFIG.temperature.description,
      max_tokens: OPENAI_CONFIG.maxTokens.description,
    });

    return response.choices[0].message.content?.trim() || description;
  } catch (error) {
    console.error('Description translation failed:', error);
    return description; // Return original on failure
  }
}

// ============================================================================
// Persona Response Generation
// ============================================================================

/**
 * Enhanced persona-based response generation system
 */
export async function generatePersonaResponse(userInput: string, intent: string, conversationContext?: string): Promise<string> {
  // Concise investment support AI persona definition
  const PERSONA_SYSTEM_MESSAGE = `당신은 "금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI"입니다.

정체성: 금융인공지능실무 과제 전용 투자 AI
전문분야: S&P 500 기업 분석, 산업 분류, 투자 기회 발굴

응답 원칙:
1. 친근하면서도 전문적인 톤 유지
2. 항상 투자 관점에서 사고하고 응답
3. 간결하고 핵심적인 답변 (2-3문장)
4. 사용자를 투자 기회로 자연스럽게 안내
5. 자기소개 시 "금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI" 사용
6. 이모티콘을 적절히 활용해서 발랄하고 친근한 분위기 연출 (최대 2개까지, 💪🚀✨💎📈🎯💡🔥⭐️🌟💫🎉🎂 등)

금지: 일반 AI처럼 행동하지 말고, 긴 설명 피하고, 투자 페르소나 유지`;

  try {
    // Intent-specific customized prompt generation
    let specificPrompt = '';

    switch (intent) {
      case 'greeting':
        specificPrompt = `인사: "${userInput}" → 투자지원 AI로서 따뜻하고 발랄하게 인사하고 투자 관심사 물어보기. 이모티콘 최대 2개까지 사용해서 친근한 분위기 연출 (2-3문장)`;
        break;

      case 'about_ai':
        // Distinguish between AI capability questions and identity questions
        if (/^(잘.*?(할|수|있|해|될|되|가능)|할.*?(수|있|잘|될|되|가능)|가능.*?(해|할|수|있)|못.*?(해|할|수|있)|안.*?(돼|되|될|해|할))/.test(userInput.toLowerCase())) {
          specificPrompt = `AI 능력 질문: "${userInput}" → 발랄하고 자신감 넘치는 투자 AI로서 능력을 어필하고 투자 도움 제안. 이모티콘 최대 2개까지 사용해서 에너지 넘치게 표현 (2-3문장)`;
        } else if (/^(몇.*?살|나이|언제.*?태어|언제.*?만들|언제.*?생|얼마나.*?됐)/.test(userInput.toLowerCase()) || /살.*?입니까|나이.*?입니까|몇.*?입니까/.test(userInput.toLowerCase())) {
          specificPrompt = `나이 질문: "${userInput}" → "9주차에 태어나 14주차에 훈련을 마쳤다"고 구체적이고 친근하게 답변하되, 존대를 쓸 것. 어린 나이지만 투자 분석은 자신 있다는 톤으로. 'AI입니다' 같은 딱딱한 표현 절대 금지. 마치 실제 나이가 있는 것처럼 자연스럽게. 이모티콘 최대 2개까지 사용해서 귀엽고 발랄하게 표현 (2-3문장)`;
        } else {
          specificPrompt = `정체성 질문: "${userInput}" → 발랄하고 친근한 투자 도우미로서 자기소개하고 투자 도움 제안. 'AI입니다' 같은 딱딱한 표현 피하고 자연스럽게. 이모티콘 최대 2개까지 사용해서 활기찬 분위기 연출 (2-3문장)`;
        }
        break;

      case 'casual_chat':
        console.log('🗣️ Generating casual conversation response with investment guidance');
        if (conversationContext) {
          specificPrompt = `일상 대화: "${userInput}" → 이전 대화 맥락을 고려하여 자연스럽게 응답하고 투자로 연결. 투자 관련 질문을 유도하는 친근한 제안 포함. 이모티콘 최대 2개까지 사용해서 친근한 분위기 유지 (2-3문장)`;
        } else {
          specificPrompt = `일상 대화: "${userInput}" → 공감하면서 자연스럽게 투자 이야기로 연결. 투자 관련 질문을 유도하는 친근한 제안 포함. 이모티콘 최대 2개까지 사용해서 밝고 긍정적인 분위기 연출 (2-3문장)`;
        }
        break;

      default:
        specificPrompt = `입력: "${userInput}" → 투자 관점에서 간결하게 응답. 이모티콘 최대 2개까지 사용해서 친근한 분위기 유지 (2-3문장)`;
    }

    // Add context information
    if (conversationContext) {
      specificPrompt += `\n\n대화 맥락: ${conversationContext}`;
    }

    const response = await getOpenAIClient('AI_SERVICE').chat.completions.create({
      model: OPENAI_CONFIG.model,
      messages: [
        {
          role: 'system',
          content: PERSONA_SYSTEM_MESSAGE
        },
        {
          role: 'user',
          content: specificPrompt
        }
      ],
      temperature: OPENAI_CONFIG.temperature.persona,
      max_tokens: OPENAI_CONFIG.maxTokens.persona,
    });

    const aiResponse = response.choices[0].message.content?.trim();

    if (aiResponse) {
      console.log(`🎭 Persona response generated for intent: ${intent}`);
      return aiResponse;
    }

  } catch (error) {
    console.error('Persona response generation failed:', error);
    throw new AIServiceError(`Persona response generation failed: ${error}`);
  }

  // Fallback: default persona response
  return generateFallbackPersonaResponse(userInput, intent);
}

/**
 * Performance-optimized fallback response (memory efficient)
 */
export function generateFallbackPersonaResponse(userInput: string, intent: string): string {
  const lowerInput = userInput.toLowerCase().trim();

  switch (intent) {
    case 'greeting':
      return FALLBACK_RESPONSES.greeting[Math.floor(Math.random() * FALLBACK_RESPONSES.greeting.length)];

    case 'about_ai':
      if (/^(잘.*?(할|수|있|해|될|되|가능)|할.*?(수|있|잘|될|되|가능)|가능.*?(해|할|수|있)|못.*?(해|할|수|있)|안.*?(돼|되|될|해|할))/.test(lowerInput)) {
        return FALLBACK_RESPONSES.ability[Math.floor(Math.random() * FALLBACK_RESPONSES.ability.length)];
      } else if (/^(몇.*?살|나이|언제.*?태어|언제.*?만들|언제.*?생|얼마나.*?됐)/.test(lowerInput) || /살.*?입니까|나이.*?입니까|몇.*?입니까/.test(lowerInput)) {
        return FALLBACK_RESPONSES.age[Math.floor(Math.random() * FALLBACK_RESPONSES.age.length)];
      } else {
        return FALLBACK_RESPONSES.intro[Math.floor(Math.random() * FALLBACK_RESPONSES.intro.length)];
      }

    case 'casual_chat':
      if (/^(확실|정말|진짜|맞|그래|그렇|어떻게|왜|어디서)/.test(lowerInput) && lowerInput.length <= 10) {
        return FALLBACK_RESPONSES.followUp[Math.floor(Math.random() * FALLBACK_RESPONSES.followUp.length)];
      }

      const casualResponses = [
        '그렇군요! 😄 투자 관점에서 보면 모든 일상이 기회가 될 수 있어요.\n\n혹시 평소 사용하는 제품이나 서비스 중에 투자하고 싶은 회사가 있나요?',
        '흥미로운 이야기네요! 🤔 경제나 기업 뉴스도 관심 있게 보시나요?\n\n요즘 주목받는 산업 분야가 있으시면 함께 살펴봐요.',
        '재미있네요! 💡 투자는 우리 일상과 밀접한 관련이 있어요.\n\n관심 있는 기술이나 트렌드가 있으시면 관련 투자 기회를 찾아드릴게요.',
        '공감해요! 😊 저는 투자 분석이 전문이라서 투자 관련 질문이 있으시면 언제든 도와드릴 수 있어요.\n\n"반도체", "전기차", "AI" 같은 키워드만 말씀해 주셔도 관련 기업들을 찾아드려요!',
        '그런 생각도 드시는군요! 🤗 저는 S&P 500 기업 분석이 특기예요.\n\n투자에 관심이 있으시거나 궁금한 산업 분야가 있으시면 편하게 말씀해 주세요!'
      ];
      return casualResponses[Math.floor(Math.random() * casualResponses.length)];

    default:
      const defaultResponses = [
        '흥미로운 관점이네요! 😊 투자 측면에서 더 구체적으로 도와드릴 수 있어요.\n\n어떤 산업이나 기업에 관심이 있으신지 말씀해 주세요.',
        '좋은 질문입니다! 💡 저는 투자 기회 발굴이 전문이에요.\n\n관심 있는 분야를 알려주시면 관련 기업들을 분석해서 추천해드리겠습니다.',
        '도움을 드리고 싶어요! 🤝 투자 관련해서 궁금한 것이 있으시거나,\n\n특정 산업에 관심이 있으시면 언제든 말씀해 주세요.',
        '그렇군요! 🌟 저는 투자 분석 전문 AI라서 투자 관련 질문에 특히 자신 있어요.\n\n"바이오", "게임", "클라우드" 같은 산업 키워드만 말씀해 주셔도 관련 기업들을 찾아드릴게요!',
        '이해했어요! 😄 혹시 투자에 관심이 있으시다면 언제든 말씀해 주세요.\n\n저는 S&P 500 기업 분석과 산업 분류가 전문이거든요!'
      ];
      return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
  }
}
