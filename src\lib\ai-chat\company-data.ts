/**
 * Company Data Access Module
 * 
 * This module provides safe access to company data and validation functions.
 * Optimized for Korean display and data integrity checks.
 */

import { QUICK_ENRICHED_FINAL as DATA } from '@/data/sp500_enriched_final';
import { CompanyData } from './types';

// ============================================================================
// Types
// ============================================================================

export interface DataValidationResult {
  totalCompanies: number;
  validCompanies: number;
  invalidCompanies: string[];
  missingFields: Record<string, string[]>;
}

export interface DatasetStats {
  totalCompanies: number;
  totalIndustries: number;
  averageDescriptionLength: number;
  longestCompanyName: string;
  shortestCompanyName: string;
}

// ============================================================================
// Core Data Access Functions
// ============================================================================

/**
 * Gets complete company data safely
 */
export function getCompanyData(ticker: string): CompanyData | null {
  const company = (DATA as any)[ticker];
  return company ? {
    name: company.name,
    industry: company.industry,
    description: company.description
  } : null;
}

/**
 * Checks if a ticker exists in the dataset
 */
export function isValidTicker(ticker: string): boolean {
  return ticker in DATA;
}

/**
 * Gets all available tickers
 */
export function getAllTickers(): string[] {
  return Object.keys(DATA);
}

/**
 * Gets all available industries
 */
export function getAllIndustries(): string[] {
  const companies = Object.values(DATA) as any[];
  return [...new Set(companies.map(c => c.industry))];
}

/**
 * Gets companies by industry
 */
export function getCompaniesByIndustry(industry: string): Array<{ticker: string, data: CompanyData}> {
  return Object.entries(DATA)
    .filter(([_, company]) => (company as any).industry === industry)
    .map(([ticker, company]) => {
      const comp = company as any;
      return {
        ticker,
        data: {
          name: comp.name,
          industry: comp.industry,
          description: comp.description
        }
      };
    });
}

/**
 * Filters companies by industry and optional name query
 */
export function filterCompanies(
  industry?: string,
  nameQuery?: string,
  limit: number = 50
): Array<{ticker: string, data: CompanyData}> {
  let companies = Object.entries(DATA).map(([ticker, company]) => {
    const comp = company as any;
    return {
      ticker,
      data: {
        name: comp.name,
        industry: comp.industry,
        description: comp.description
      }
    };
  });
  
  // Filter by industry if specified
  if (industry) {
    companies = companies.filter(({ data }) => data.industry === industry);
  }
  
  // Filter by name query if specified
  if (nameQuery) {
    const normalizedQuery = nameQuery.toLowerCase().trim();
    companies = companies.filter(({ data }) => 
      data.name.toLowerCase().includes(normalizedQuery)
    );
  }
  
  // Apply limit
  return companies.slice(0, limit);
}

// ============================================================================
// Data Validation Functions
// ============================================================================

/**
 * Validates the integrity of the company dataset
 */
export function validateDataset(): DataValidationResult {
  const totalCompanies = Object.keys(DATA).length;
  let validCompanies = 0;
  const invalidCompanies: string[] = [];
  const missingFields: Record<string, string[]> = {};
  
  for (const [ticker, company] of Object.entries(DATA)) {
    const comp = company as any;
    const missing: string[] = [];
    
    if (!comp.name || comp.name.trim() === '') missing.push('name');
    if (!comp.industry || comp.industry.trim() === '') missing.push('industry');
    if (!comp.description || comp.description.trim() === '') missing.push('description');
    
    if (missing.length > 0) {
      invalidCompanies.push(ticker);
      missingFields[ticker] = missing;
    } else {
      validCompanies++;
    }
  }
  
  return {
    totalCompanies,
    validCompanies,
    invalidCompanies,
    missingFields
  };
}

/**
 * Gets dataset statistics
 */
export function getDatasetStats(): DatasetStats {
  const companies = Object.values(DATA) as any[];
  const industries = new Set(companies.map(c => c.industry));
  
  const descriptionLengths = companies.map(c => c.description.length);
  const averageDescriptionLength = descriptionLengths.reduce((a, b) => a + b, 0) / descriptionLengths.length;
  
  const companyNames = companies.map(c => c.name);
  const longestCompanyName = companyNames.reduce((a, b) => a.length > b.length ? a : b);
  const shortestCompanyName = companyNames.reduce((a, b) => a.length < b.length ? a : b);
  
  return {
    totalCompanies: companies.length,
    totalIndustries: industries.size,
    averageDescriptionLength: Math.round(averageDescriptionLength),
    longestCompanyName,
    shortestCompanyName
  };
}

/**
 * Searches companies by name (case-insensitive)
 */
export function searchCompaniesByName(query: string, limit: number = 10): Array<{ticker: string, data: CompanyData}> {
  const normalizedQuery = query.toLowerCase().trim();
  
  if (!normalizedQuery) {
    return [];
  }
  
  const matches = Object.entries(DATA)
    .filter(([_, company]) => {
      const comp = company as any;
      return comp.name.toLowerCase().includes(normalizedQuery);
    })
    .slice(0, limit)
    .map(([ticker, company]) => {
      const comp = company as any;
      return {
        ticker,
        data: {
          name: comp.name,
          industry: comp.industry,
          description: comp.description
        }
      };
    });
  
  return matches;
}

/**
 * Gets random companies from a specific industry
 */
export function getRandomCompaniesFromIndustry(industry: string, count: number = 3): Array<{ticker: string, data: CompanyData}> {
  const industryCompanies = getCompaniesByIndustry(industry);
  
  if (industryCompanies.length === 0) {
    return [];
  }
  
  // Shuffle and take the requested count
  const shuffled = [...industryCompanies].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, Math.min(count, shuffled.length));
}

/**
 * Gets companies with the longest descriptions (for analysis)
 */
export function getCompaniesWithLongestDescriptions(limit: number = 5): Array<{ticker: string, data: CompanyData, descriptionLength: number}> {
  return Object.entries(DATA)
    .map(([ticker, company]) => {
      const comp = company as any;
      return {
        ticker,
        data: {
          name: comp.name,
          industry: comp.industry,
          description: comp.description
        },
        descriptionLength: comp.description.length
      };
    })
    .sort((a, b) => b.descriptionLength - a.descriptionLength)
    .slice(0, limit);
}
