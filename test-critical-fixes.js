/**
 * Test script to verify the three critical fixes
 */

const http = require('http');

// Test 1: Direct Ticker Matching (bypasses GPT)
async function testDirectTickerMatching() {
  console.log('🧪 Test 1: Testing Direct Ticker Matching...');
  
  const testTickers = ['NVDA', 'WAB', 'TSLA', 'AAPL'];
  let successCount = 0;
  
  for (const ticker of testTickers) {
    try {
      console.log(`   Testing ticker: ${ticker}`);
      const response = await makeAPIRequest('/api/ai_chat', {
        message: ticker
      });
      
      if (response.statusCode === 200) {
        console.log(`   ✅ ${ticker} processed successfully`);
        console.log(`   Response: ${response.data.reply?.substring(0, 50)}...`);
        successCount++;
      } else {
        console.log(`   ❌ ${ticker} failed with status ${response.statusCode}`);
        console.log(`   Error: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.log(`   ❌ ${ticker} failed with error: ${error.message}`);
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  const successRate = (successCount / testTickers.length) * 100;
  console.log(`✅ Test 1 Result: ${successCount}/${testTickers.length} tickers processed (${successRate}%)`);
  return successCount >= 3; // At least 75% success rate
}

// Test 2: "더보기" Command (bypasses GPT)
async function testShowMoreCommand() {
  console.log('\n🧪 Test 2: Testing "더보기" Command...');
  
  try {
    // First, trigger an industry query to get to SHOW_INDUSTRY stage
    console.log('   Step 1: Triggering industry query...');
    const response1 = await makeAPIRequest('/api/ai_chat', {
      message: "반도체"  // Semiconductor industry
    });
    
    if (response1.statusCode === 200) {
      console.log('   ✅ Industry query successful');
      
      // Now test "더보기" command
      console.log('   Step 2: Testing "더보기" command...');
      const response2 = await makeAPIRequest('/api/ai_chat', {
        message: "더보기"
      });
      
      if (response2.statusCode === 200) {
        console.log('   ✅ "더보기" command processed successfully');
        console.log(`   Response: ${response2.data.reply?.substring(0, 50)}...`);
        return true;
      } else {
        console.log('   ❌ "더보기" command failed');
        console.log(`   Status: ${response2.statusCode}`);
        console.log(`   Error: ${JSON.stringify(response2.data)}`);
        return false;
      }
    } else {
      console.log('   ❌ Industry query failed');
      console.log(`   Status: ${response1.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log('   ❌ Test 2 failed with error:', error.message);
    return false;
  }
}

// Test 3: Check server logs for cache errors
function testCacheSystem() {
  console.log('\n🧪 Test 3: Testing Cache System...');
  
  // This test relies on manual inspection of server logs
  console.log('   ✅ Check server logs for:');
  console.log('   • "✅ Embeddings system initialized successfully"');
  console.log('   • No "such file or directory" errors');
  console.log('   • Proper cache directory creation messages');
  console.log('   • No cache-related failures');
  
  return true; // Manual verification required
}

// Test 4: JSON Parsing Robustness
async function testJSONParsingRobustness() {
  console.log('\n🧪 Test 4: Testing JSON Parsing Robustness...');
  
  try {
    // Test various inputs that might cause JSON parsing issues
    const testInputs = [
      "WAB",      // Ticker that previously caused JSON issues
      "네",       // Korean positive response
      "아니요",   // Korean negative response
      "더보기"    // Show more command
    ];
    
    let successCount = 0;
    
    for (const input of testInputs) {
      try {
        const response = await makeAPIRequest('/api/ai_chat', {
          message: input
        });
        
        if (response.statusCode === 200) {
          console.log(`   ✅ "${input}" processed without JSON errors`);
          successCount++;
        } else {
          console.log(`   ❌ "${input}" failed with status ${response.statusCode}`);
        }
      } catch (error) {
        console.log(`   ❌ "${input}" failed with error: ${error.message}`);
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    const successRate = (successCount / testInputs.length) * 100;
    console.log(`✅ Test 4 Result: ${successCount}/${testInputs.length} inputs processed (${successRate}%)`);
    return successCount >= 3; // At least 75% success rate
    
  } catch (error) {
    console.log('   ❌ Test 4 failed with error:', error.message);
    return false;
  }
}

// Helper function to make API requests
function makeAPIRequest(path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: data ? 'POST' : 'GET',
      headers: data ? {
        'Content-Type': 'application/json',
        'Content-Length': JSON.stringify(data).length
      } : {}
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = body ? JSON.parse(body) : {};
          resolve({
            statusCode: res.statusCode,
            data: parsedData
          });
        } catch (parseError) {
          resolve({
            statusCode: res.statusCode,
            data: { error: 'Failed to parse response', body: body }
          });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Testing Three Critical Fixes...\n');
  
  const results = {
    directTicker: await testDirectTickerMatching(),
    showMore: await testShowMoreCommand(),
    cache: testCacheSystem(),
    jsonParsing: await testJSONParsingRobustness()
  };
  
  console.log('\n' + '='.repeat(80));
  console.log('📊 CRITICAL FIXES TEST RESULTS');
  console.log('='.repeat(80));
  
  console.log(`✅ Fix 1 - Direct Ticker Matching: ${results.directTicker ? 'PASSED' : 'FAILED'}`);
  console.log(`✅ Fix 2 - "더보기" Command Bypass: ${results.showMore ? 'PASSED' : 'FAILED'}`);
  console.log(`✅ Fix 3 - Cache System Errors: ${results.cache ? 'PASSED' : 'FAILED'} (Manual verification)`);
  console.log(`✅ Fix 4 - JSON Parsing Robustness: ${results.jsonParsing ? 'PASSED' : 'FAILED'}`);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n📈 Overall Success Rate: ${passedTests}/${totalTests} (${Math.round(passedTests/totalTests*100)}%)`);
  
  if (passedTests === totalTests) {
    console.log('🎉 ALL CRITICAL FIXES WORKING!');
  } else {
    console.log('⚠️  Some fixes need additional attention.');
  }
  
  console.log('\n📋 Expected Server Log Improvements:');
  console.log('• "🎯 Direct ticker bypass: NVDA -> Nvidia" (for ticker inputs)');
  console.log('• "🔍 Detected 더보기 command, bypassing GPT classification"');
  console.log('• "✅ Embeddings system initialized successfully"');
  console.log('• "✅ Fixed JSON parsing successful" (for malformed JSON recovery)');
  console.log('• No "Unterminated string in JSON" errors');
  console.log('='.repeat(80));
}

// Run tests
runAllTests().catch(console.error);
