#!/usr/bin/env node

/**
 * Test Runner for Refactored AI Chat System
 * 
 * This script runs comprehensive tests on the refactored AI chat system
 * to validate all functionality works correctly with Korean input.
 */

const { runComprehensiveSystemTests, quickHealthCheck } = require('../src/lib/ai-chat/company-utils');

async function main() {
  console.log('🚀 Starting AI Chat System Tests...');
  console.log('');
  
  try {
    // Quick health check first
    console.log('🔍 Running quick health check...');
    const isHealthy = await quickHealthCheck();
    
    if (!isHealthy) {
      console.error('❌ System health check failed! Aborting comprehensive tests.');
      process.exit(1);
    }
    
    console.log('✅ System health check passed!');
    console.log('');
    
    // Run comprehensive tests
    console.log('🧪 Running comprehensive system tests...');
    const report = await runComprehensiveSystemTests();
    
    // Display results
    console.log('');
    console.log('📋 DETAILED TEST RESULTS:');
    console.log('');
    
    report.results.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.testName}`);
      
      if (!result.passed && result.error) {
        console.log(`   Error: ${result.error}`);
      }
      
      if (result.details) {
        console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
      }
      
      console.log('');
    });
    
    // Exit with appropriate code
    if (report.failedTests > 0) {
      console.error(`❌ ${report.failedTests} test(s) failed. Please fix the issues before deployment.`);
      process.exit(1);
    } else {
      console.log('🎉 All tests passed! The refactored system is ready for use.');
      process.exit(0);
    }
    
  } catch (error) {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the tests
main();
