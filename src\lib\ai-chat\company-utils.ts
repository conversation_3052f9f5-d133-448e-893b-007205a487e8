/**
 * Company Utilities Module (Refactored)
 *
 * This module provides a simplified interface to company-related operations.
 * Most functionality has been moved to specialized modules for better maintainability.
 * Optimized for Korean input only.
 */

// Re-export functions from specialized modules
export {
  findTickerWithPrecedence,
  isValidTicker,
  getCompanyName,
  getDirectTickerMatch,
  findTickerInKoreanText,
  type TickerMatchResult
} from './ticker-matching';

export {
  isPositive,
  isNegative,
  getResponseType,
  getDetailedResponseClassification,
  type ResponseClassificationResult
} from './response-classification';

export {
  getCompanyData,
  getAllTickers,
  getAllIndustries,
  getCompaniesByIndustry,
  filterCompanies,
  validateDataset,
  getDatasetStats,
  searchCompaniesByName,
  getRandomCompaniesFromIndustry,
  type DataValidationResult,
  type DatasetStats
} from './company-data';

export {
  generateRandomRecommendation,
  generateIndustryRecommendation,
  getTopCompaniesByIndustry,
  translateAndFormatRecommendations,
  formatCompanyDescriptions,
  formatCompanyDisplay,
  formatCompanyList,
  formatTickerList,
  formatIndustryInfo,
  getRandomCompanySample,
  getCompaniesBySimilarIndustry,
  validateRecommendation,
  type FormattedRecommendation
} from './recommendation-generator';

// ============================================================================
// Testing and Validation Exports
// ============================================================================

export {
  runComprehensiveSystemTests,
  testSystemPerformance,
  quickHealthCheck,
  type SystemTestReport,
  type TestResult
} from './system-tests';

// ============================================================================
// Refactoring Complete
// ============================================================================

// All functions have been moved to specialized modules for better maintainability:
// - ticker-matching.ts: Ticker matching and validation functions
// - response-classification.ts: Korean response classification with GPT
// - company-data.ts: Company data access and validation functions
// - recommendation-generator.ts: Investment recommendation generation and formatting
// - system-tests.ts: Comprehensive testing and validation suite
//
// This file now serves as a clean re-export interface for backward compatibility.



