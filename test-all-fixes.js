/**
 * Comprehensive test script to verify all four critical fixes
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

// Test 1: Verify SpeedTraffic JSON storage removal
function testSpeedTrafficStorageRemoval() {
  console.log('🧪 Test 1: Verifying SpeedTraffic JSON storage removal...');
  
  // Check if save_analysis_results.ts was removed
  const saveApiPath = path.join(__dirname, 'src', 'pages', 'api', 'save_analysis_results.ts');
  const resultsStoragePath = path.join(__dirname, 'src', 'utils', 'resultsStorage.ts');
  
  const saveApiExists = fs.existsSync(saveApiPath);
  const resultsStorageExists = fs.existsSync(resultsStoragePath);
  
  if (!saveApiExists && !resultsStorageExists) {
    console.log('✅ Test 1 PASSED: SpeedTraffic JSON storage files removed');
    return true;
  } else {
    console.log('❌ Test 1 FAILED: Some storage files still exist');
    console.log(`   save_analysis_results.ts exists: ${saveApiExists}`);
    console.log(`   resultsStorage.ts exists: ${resultsStorageExists}`);
    return false;
  }
}

// Test 2: Test GPT response parsing robustness
async function testGPTResponseParsing() {
  console.log('\n🧪 Test 2: Testing GPT response parsing robustness...');
  
  try {
    const response = await makeAPIRequest('/api/ai_chat', {
      message: "네"  // This should trigger response type classification
    });
    
    if (response.statusCode === 200) {
      console.log('✅ Test 2 PASSED: GPT response parsing handled successfully');
      console.log(`   Response: ${response.data.reply?.substring(0, 50)}...`);
      return true;
    } else {
      console.log('❌ Test 2 FAILED: API request failed');
      console.log(`   Status: ${response.statusCode}`);
      console.log(`   Error: ${JSON.stringify(response.data)}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Test 2 FAILED: Exception occurred');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Test 3: Test SpeedTraffic component analysis logging
async function testSpeedTrafficLogging() {
  console.log('\n🧪 Test 3: Testing SpeedTraffic component analysis logging...');
  
  try {
    console.log('   Triggering SpeedTraffic analysis for NVDA...');
    
    // Test Phase 1 (Technical, Industry, Market, Risk Analysis)
    const phase1Response = await makeAPIRequest('/api/lstm_prediction_simple?symbol=NVDA&stage=phase1');
    
    if (phase1Response.statusCode === 200) {
      console.log('✅ Test 3a PASSED: Phase 1 analysis completed successfully');
      console.log('   Check server logs for detailed component analysis results');
      
      // Test Phase 2 (Neural Network Analysis)
      const phase2Response = await makeAPIRequest('/api/lstm_prediction_simple?symbol=NVDA&stage=phase2');
      
      if (phase2Response.statusCode === 200) {
        console.log('✅ Test 3b PASSED: Phase 2 (Neural Network) analysis completed successfully');
        console.log('   Check server logs for LSTM analysis results');
        return true;
      } else {
        console.log('❌ Test 3b FAILED: Phase 2 analysis failed');
        return false;
      }
    } else {
      console.log('❌ Test 3a FAILED: Phase 1 analysis failed');
      console.log(`   Status: ${phase1Response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Test 3 FAILED: Exception occurred');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Test 4: Test simplified intent classification logging
async function testSimplifiedLogging() {
  console.log('\n🧪 Test 4: Testing simplified intent classification logging...');
  
  try {
    // Test various inputs to trigger different intent classifications
    const testInputs = [
      "안녕하세요",      // greeting
      "테슬라",          // company_direct
      "투자 추천해줘",    // investment_recommendation
      "네",             // positive_response
      "아니요"          // negative_response
    ];
    
    let successCount = 0;
    
    for (const input of testInputs) {
      try {
        const response = await makeAPIRequest('/api/ai_chat', { message: input });
        if (response.statusCode === 200) {
          successCount++;
          console.log(`   ✅ "${input}" processed successfully`);
        } else {
          console.log(`   ❌ "${input}" failed with status ${response.statusCode}`);
        }
      } catch (error) {
        console.log(`   ❌ "${input}" failed with error: ${error.message}`);
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    if (successCount >= 3) {
      console.log('✅ Test 4 PASSED: Intent classification logging working');
      console.log('   Check server logs for simplified logging format (intent + confidence only)');
      return true;
    } else {
      console.log('❌ Test 4 FAILED: Too many intent classification failures');
      return false;
    }
  } catch (error) {
    console.log('❌ Test 4 FAILED: Exception occurred');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Helper function to make API requests
function makeAPIRequest(path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: data ? 'POST' : 'GET',
      headers: data ? {
        'Content-Type': 'application/json',
        'Content-Length': JSON.stringify(data).length
      } : {}
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = body ? JSON.parse(body) : {};
          resolve({
            statusCode: res.statusCode,
            data: parsedData
          });
        } catch (parseError) {
          resolve({
            statusCode: res.statusCode,
            data: { error: 'Failed to parse response', body: body }
          });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Running comprehensive test suite for all four critical fixes...\n');
  
  const results = {
    test1: testSpeedTrafficStorageRemoval(),
    test2: await testGPTResponseParsing(),
    test3: await testSpeedTrafficLogging(),
    test4: await testSimplifiedLogging()
  };
  
  console.log('\n' + '='.repeat(80));
  console.log('📊 COMPREHENSIVE TEST RESULTS SUMMARY');
  console.log('='.repeat(80));
  
  console.log(`✅ Fix 1 - SpeedTraffic JSON Storage Removal: ${results.test1 ? 'PASSED' : 'FAILED'}`);
  console.log(`✅ Fix 2 - SpeedTraffic Component Logging: ${results.test3 ? 'PASSED' : 'FAILED'}`);
  console.log(`✅ Fix 3 - GPT Response Parsing Error: ${results.test2 ? 'PASSED' : 'FAILED'}`);
  console.log(`✅ Fix 4 - Simplified Intent Logging: ${results.test4 ? 'PASSED' : 'FAILED'}`);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n📈 Overall Success Rate: ${passedTests}/${totalTests} (${Math.round(passedTests/totalTests*100)}%)`);
  
  if (passedTests === totalTests) {
    console.log('🎉 ALL FIXES IMPLEMENTED SUCCESSFULLY!');
  } else {
    console.log('⚠️  Some fixes need additional attention.');
  }
  
  console.log('='.repeat(80));
}

// Run tests
runAllTests().catch(console.error);
