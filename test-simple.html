<!DOCTYPE html>
<html>
<head>
    <title>AI Chat Test</title>
</head>
<body>
    <h1>AI Chat Test</h1>
    <div>
        <input type="text" id="messageInput" placeholder="Type your message..." style="width: 300px;">
        <button onclick="sendMessage()">Send</button>
    </div>
    <div id="response" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; min-height: 100px;"></div>

    <script>
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const responseDiv = document.getElementById('response');
            const message = input.value.trim();
            
            if (!message) return;
            
            responseDiv.innerHTML = 'Sending...';
            
            try {
                const response = await fetch('/api/ai_chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    responseDiv.innerHTML = `
                        <strong>You:</strong> ${message}<br>
                        <strong>AI:</strong> ${data.reply}<br>
                        <small>Status: ${response.status}</small>
                    `;
                } else {
                    responseDiv.innerHTML = `
                        <strong>Error:</strong> ${JSON.stringify(data)}<br>
                        <small>Status: ${response.status}</small>
                    `;
                }
                
                input.value = '';
            } catch (error) {
                responseDiv.innerHTML = `<strong>Network Error:</strong> ${error.message}`;
            }
        }
        
        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
