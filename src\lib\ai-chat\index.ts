/**
 * AI Chat System - Public API
 *
 * This module exports the public interface for the AI chat system.
 * It provides a clean, modular API while maintaining backward compatibility.
 */

// ============================================================================
// Import Dependencies
// ============================================================================

import { getEmbeddings } from '@/lib/embeddings';

// ============================================================================
// Main Request Handler (Primary Export)
// ============================================================================

export {
  handleChatRequest,
  resetSessionAfterChart,
  getSessionStatistics,
  performHealthCheck
} from './request-handler';

// ============================================================================
// Type Exports
// ============================================================================

export type {
  // Core types
  LSTMData,
  Stage,
  SessionState,
  IntentClassificationResult,
  PersonaContext,
  RAGThresholds,
  IndustryMatchResult,
  CompanyRecommendation,
  RandomRecommendation,
  KoreanCompanyMapping,
  ParsedRequest,
  ChatResponse,
  PipelineContext,
  StageHandlerResult,
  OpenAIConfig,
  PatternConfig,
  CompanyData,
  
  // Error types
  AIChatError,
  SessionError,
  AIServiceError,
  RAGServiceError
} from './types';

// ============================================================================
// Configuration Exports
// ============================================================================

export {
  RAG_THRESHOLDS,
  PATTERNS,
  OPENAI_CONFIG,
  SESSION_CONFIG,
  PERFORMANCE_CONFIG,
  KOREAN_COMPANY_MAPPING,
  FALLBACK_RESPONSES,
  WELCOME_MESSAGES,
  ENV_CONFIG
} from './config';

// ============================================================================
// Session Management Exports
// ============================================================================

export {
  createNewSession,
  generateSessionId,
  getSession,
  updateSession,
  deleteSession,
  resetSession,
  sessionExists,
  getActiveSessionCount,
  addConversationEntry,
  getRecentConversationContext,
  clearConversationHistory,
  cleanupOldSessions,
  startSessionCleanup,
  stopSessionCleanup,
  clearAllSessions,
  updateSessionStage,
  updateSelectedIndustry,
  updateIndustryCompanies,
  updateSelectedTicker,
  getSessionStats,
  initializeSessionManager,
  shutdownSessionManager
} from './session-manager';

// ============================================================================
// AI Service Exports
// ============================================================================

export {
  classifyUserIntent,
  classifyIndustryWithGPT,
  translateDescription,
  generatePersonaResponse,
  generateFallbackPersonaResponse
} from './ai-service';

// ============================================================================
// RAG Service Exports
// ============================================================================

export {
  findBestIndustry,
  findCompanyInAllData,
  findTickerInText,
  getIndustryCompanies,
  getAllAvailableIndustries,
  testRAGThresholds
} from './rag-service';

// ============================================================================
// LSTM Service Exports
// ============================================================================

export {
  getLSTMDataForSymbol,
  getAvailableLSTMSymbols,
  enhanceResponseWithLSTMData,
  getDetailedLSTMAnalysis,
  isLSTMDataAvailable,
  getBatchLSTMData,
  formatLSTMDataForDisplay,
  checkLSTMServiceHealth,
  lstmSymbolCache,
  getCachedAvailableLSTMSymbols,
  shouldEnhanceWithLSTM,
  getLSTMAvailabilitySummary
} from './lstm-service';

// ============================================================================
// Company Utilities Exports
// ============================================================================

export {
  isPositive,
  isNegative,
  getResponseType,
  getCompanyName,
  getCompanyData,
  isValidTicker,
  getAllTickers,
  getCompaniesByIndustry,
  generateRandomRecommendation,
  generateMultipleRecommendations,
  formatCompanyDisplay,
  formatCompanyList,
  translateAndFormatRecommendations,
  formatCompanyDescriptions,
  getIndustryStats,
  getTopIndustries,
  getCompaniesWithDetailedDescriptions,
  searchCompaniesByName,
  filterCompanies,
  validateCompanyData,
  getDatasetStats
} from './company-utils';

// ============================================================================
// Pipeline Handlers Exports (for advanced usage)
// ============================================================================

export {
  handleStartStage,
  handleShowIndustryStage,
  handleAskChartStage
} from './pipeline-handlers';

// ============================================================================
// Backward Compatibility Exports
// ============================================================================

/**
 * Legacy function exports for backward compatibility
 * These maintain the same interface as the original ai_chat.ts
 */

// Main handler (same interface as original)
export { handleChatRequest as default } from './request-handler';

// Configuration export (same as original)
export const config = { api: { bodyParser: { sizeLimit: '1mb' } } };

// ============================================================================
// Utility Functions for Testing and Debugging
// ============================================================================

/**
 * Creates a test context for pipeline testing
 */
export function createTestContext(
  userInput: string,
  sessionId: string = 'test-session',
  stage: Stage = 'START'
): PipelineContext {
  const { createNewSession } = require('./session-manager');
  const state = createNewSession();
  state.stage = stage;
  
  return {
    userInput,
    sessionId,
    state
  };
}

/**
 * Validates the system configuration
 */
export function validateSystemConfiguration(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check environment variables
  if (!process.env.OPENAI_API_KEY) {
    errors.push('OPENAI_API_KEY environment variable is not set');
  }
  
  // Check data availability
  try {
    const { QUICK_ENRICHED_FINAL } = require('@/data/sp500_enriched_final');
    if (!QUICK_ENRICHED_FINAL || Object.keys(QUICK_ENRICHED_FINAL).length === 0) {
      errors.push('S&P 500 data is not available or empty');
    }
  } catch (error) {
    errors.push('Failed to load S&P 500 data');
  }
  
  // Check embeddings module with improved error handling
  try {
    // Check if getEmbeddings function is available
    if (typeof getEmbeddings !== 'function') {
      warnings.push('Embeddings function may not be available');
      console.warn('⚠️ getEmbeddings function not found in embeddings module');
    } else {
      console.log('✅ Embeddings module and getEmbeddings function loaded successfully');
      // Additional check for embeddings data availability
      try {
        const fs = require('fs');
        const path = require('path');
        const embeddingsCachePath = path.join(process.cwd(), '.cache', 'sp500_vectors.json');
        if (!fs.existsSync(embeddingsCachePath)) {
          // This is normal - embeddings will be created on first use
          console.log('📝 Embeddings cache not found - will be created on first use');
        } else {
          console.log('✅ Embeddings cache file found');
        }
      } catch (cacheError) {
        // Non-critical error
        console.log('📝 Embeddings cache check skipped:', cacheError instanceof Error ? cacheError.message : 'Unknown error');
      }
    }
  } catch (error) {
    console.error('❌ Embeddings module import error:', error);
    warnings.push(`Embeddings module may not be available: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Gets system information
 */
export function getSystemInfo(): {
  version: string;
  modules: string[];
  configuration: Record<string, any>;
  stats: Record<string, any>;
} {
  const modules = [
    'types',
    'config', 
    'session-manager',
    'ai-service',
    'rag-service',
    'lstm-service',
    'company-utils',
    'pipeline-handlers',
    'request-handler'
  ];
  
  let configuration = {};
  try {
    const { RAG_THRESHOLDS, PERFORMANCE_CONFIG } = require('./config');
    configuration = {
      ragThresholds: RAG_THRESHOLDS,
      performanceConfig: PERFORMANCE_CONFIG
    };
  } catch (error) {
    configuration = { error: 'Failed to load configuration' };
  }

  let stats = {};
  try {
    const { getSessionStatistics } = require('./request-handler');
    stats = getSessionStatistics();
  } catch (error) {
    stats = { error: 'Failed to get session statistics' };
  }
  
  return {
    version: '2.0.0', // Refactored version
    modules,
    configuration,
    stats
  };
}

// ============================================================================
// Module Initialization
// ============================================================================

/**
 * Initializes the AI chat system
 * This is called automatically when the module is imported
 */
function initializeSystem(): void {
  try {
    // Session manager is auto-initialized
    console.log('🚀 AI Chat System initialized successfully');

    // Validate configuration
    const validation = validateSystemConfiguration();
    if (!validation.isValid) {
      console.error('❌ System configuration validation failed:', validation.errors);
    }
    if (validation.warnings.length > 0) {
      console.warn('⚠️ System configuration warnings:', validation.warnings);
    } else {
      console.log('✅ All system components validated successfully');
    }

    // Initialize embeddings system asynchronously (non-blocking)
    initializeEmbeddingsAsync();

  } catch (error) {
    console.error('❌ Failed to initialize AI Chat System:', error);
  }
}

/**
 * Asynchronously initializes the embeddings system with improved error handling
 */
async function initializeEmbeddingsAsync(): Promise<void> {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [EMBEDDINGS_INIT] 🚀 Starting embeddings system initialization...`);

  try {
    // Check if getEmbeddings function is available
    if (typeof getEmbeddings !== 'function') {
      throw new Error('getEmbeddings function not found in embeddings module');
    }

    console.log(`[${timestamp}] [EMBEDDINGS_INIT] ✅ Embeddings module imported successfully`);

    // Pre-load embeddings to ensure they're available
    const embeddingsData = await getEmbeddings();

    if (embeddingsData && embeddingsData.companies && embeddingsData.industries) {
      console.log(`[${timestamp}] [EMBEDDINGS_INIT] ✅ Embeddings system initialized successfully`);
      console.log(`[${timestamp}] [EMBEDDINGS_INIT] 📊 Loaded ${embeddingsData.companies.length} companies and ${embeddingsData.industries.length} industries`);
    } else {
      throw new Error('Invalid embeddings data structure');
    }
  } catch (error) {
    console.warn(`[${timestamp}] [EMBEDDINGS_INIT] ⚠️ Embeddings system initialization deferred:`, error instanceof Error ? error.message : 'Unknown error');
    console.warn(`[${timestamp}] [EMBEDDINGS_INIT] 📝 Embeddings will be initialized on first use`);
  }
}

// Auto-initialize when module is imported
initializeSystem();

// Initialize embeddings system asynchronously
initializeEmbeddingsAsync().catch(error => {
  console.warn('⚠️ Embeddings async initialization failed:', error instanceof Error ? error.message : 'Unknown error');
});
