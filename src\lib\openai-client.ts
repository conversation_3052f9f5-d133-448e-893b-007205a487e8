/**
 * Centralized OpenAI Client Factory
 * 
 * This module provides a centralized, lazy-initialized OpenAI client
 * to ensure consistent behavior across all modules and prevent
 * environment variable loading issues.
 */

import OpenAI from 'openai';

/**
 * Global OpenAI client instance (lazy initialization)
 */
let globalOpenAIClient: OpenAI | null = null;

/**
 * One-time API key validation cache
 */
let apiKeyValidationCache: {
  isValid: boolean;
  timestamp: number;
  error?: string;
} | null = null;

/**
 * Cache duration for API key validation (24 hours)
 */
const VALIDATION_CACHE_DURATION = 24 * 60 * 60 * 1000;

/**
 * Environment variable validation and logging
 */
function validateEnvironment(): { isValid: boolean; details: any } {
  const timestamp = new Date().toISOString();
  const apiKey = process.env.OPENAI_API_KEY;
  
  const details = {
    NODE_ENV: process.env.NODE_ENV,
    OPENAI_API_KEY_EXISTS: !!apiKey,
    OPENAI_API_KEY_LENGTH: apiKey?.length || 0,
    OPENAI_API_KEY_PREFIX: apiKey?.substring(0, 10) || 'undefined',
    PROCESS_ENV_KEYS_COUNT: Object.keys(process.env).length
  };
  
  console.log(`[${timestamp}] [OPENAI_CLIENT] Environment validation:`, details);
  
  if (!apiKey) {
    console.error(`[${timestamp}] [OPENAI_CLIENT] ❌ CRITICAL: OpenAI API key not found in environment variables`);
    console.error(`[${timestamp}] [OPENAI_CLIENT] Available env vars:`, Object.keys(process.env).filter(key => key.includes('OPENAI')));
    return { isValid: false, details };
  }
  
  if (apiKey.length < 20) {
    console.error(`[${timestamp}] [OPENAI_CLIENT] ❌ WARNING: OpenAI API key seems too short (${apiKey.length} characters)`);
    return { isValid: false, details };
  }
  
  console.log(`[${timestamp}] [OPENAI_CLIENT] ✅ Environment validation passed`);
  return { isValid: true, details };
}

/**
 * Request-level tracking to reduce redundant logging
 */
let currentRequestModules = new Set<string>();
let lastRequestTime = 0;

/**
 * Get OpenAI client instance with centralized lazy initialization
 *
 * @param moduleName - Name of the module requesting the client (for logging)
 * @returns OpenAI client instance
 */
export function getOpenAIClient(moduleName: string = 'UNKNOWN'): OpenAI {
  const timestamp = new Date().toISOString();
  const currentTime = Date.now();

  // Reset tracking for new requests (after 5 seconds of inactivity)
  if (currentTime - lastRequestTime > 5000) {
    currentRequestModules.clear();
  }
  lastRequestTime = currentTime;

  if (!globalOpenAIClient) {
    console.log(`[${timestamp}] [OPENAI_CLIENT] Initializing OpenAI client for module: ${moduleName}`);

    // Validate environment
    const validation = validateEnvironment();
    if (!validation.isValid) {
      const errorMsg = `OpenAI API key is not configured properly. Please check your environment variables.`;
      console.error(`[${timestamp}] [OPENAI_CLIENT] INITIALIZATION FAILED for ${moduleName}:`, errorMsg);
      console.error(`[${timestamp}] [OPENAI_CLIENT] Stack trace:`, new Error().stack);
      throw new Error(errorMsg);
    }

    try {
      globalOpenAIClient = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY!
      });

      console.log(`[${timestamp}] [OPENAI_CLIENT] ✅ OpenAI client successfully initialized for ${moduleName}`);
      console.log(`[${timestamp}] [OPENAI_CLIENT] Client details:`, {
        hasClient: !!globalOpenAIClient,
        apiKeyLength: process.env.OPENAI_API_KEY!.length,
        moduleName
      });

      currentRequestModules.add(moduleName);

    } catch (error) {
      console.error(`[${timestamp}] [OPENAI_CLIENT] ❌ Failed to create OpenAI client for ${moduleName}:`, error);
      globalOpenAIClient = null; // Reset on failure
      throw error;
    }
  } else {
    // Only log if this is the first time this module is accessing the client in this request
    if (!currentRequestModules.has(moduleName)) {
      console.log(`[${timestamp}] [OPENAI_CLIENT] Using existing OpenAI client for module: ${moduleName}`);
      currentRequestModules.add(moduleName);
    }
    // Subsequent calls from the same module in the same request are silent
  }

  return globalOpenAIClient;
}

/**
 * Reset the global OpenAI client (useful for testing)
 */
export function resetOpenAIClient(): void {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [OPENAI_CLIENT] Resetting global OpenAI client`);
  globalOpenAIClient = null;
}

/**
 * Check if OpenAI client is initialized
 */
export function isOpenAIClientInitialized(): boolean {
  return globalOpenAIClient !== null;
}

/**
 * One-time API key validation with caching
 */
export function validateOpenAIConfiguration(): void {
  const timestamp = new Date().toISOString();
  const currentTime = Date.now();

  // Check if we have a valid cached result
  if (apiKeyValidationCache &&
      apiKeyValidationCache.isValid &&
      (currentTime - apiKeyValidationCache.timestamp) < VALIDATION_CACHE_DURATION) {
    console.log(`[${timestamp}] [OPENAI_CLIENT] ✅ Using cached OpenAI validation result (valid for ${Math.round((VALIDATION_CACHE_DURATION - (currentTime - apiKeyValidationCache.timestamp)) / (60 * 60 * 1000))} more hours)`);
    return;
  }

  // Check if we have a cached error that's still fresh (don't retry immediately)
  if (apiKeyValidationCache &&
      !apiKeyValidationCache.isValid &&
      (currentTime - apiKeyValidationCache.timestamp) < 60000) { // 1 minute cooldown for errors
    console.log(`[${timestamp}] [OPENAI_CLIENT] ❌ Using cached validation error (retry in ${Math.round((60000 - (currentTime - apiKeyValidationCache.timestamp)) / 1000)} seconds)`);
    throw new Error(apiKeyValidationCache.error || 'OpenAI configuration validation failed');
  }

  console.log(`[${timestamp}] [OPENAI_CLIENT] 🚀 Performing one-time OpenAI configuration validation...`);

  try {
    const validation = validateEnvironment();
    if (validation.isValid) {
      // Cache successful validation
      apiKeyValidationCache = {
        isValid: true,
        timestamp: currentTime
      };
      console.log(`[${timestamp}] [OPENAI_CLIENT] ✅ OpenAI configuration validated and cached for 24 hours`);
    } else {
      // Cache validation failure
      const errorMessage = 'OpenAI configuration validation failed';
      apiKeyValidationCache = {
        isValid: false,
        timestamp: currentTime,
        error: errorMessage
      };
      console.error(`[${timestamp}] [OPENAI_CLIENT] ❌ OpenAI configuration validation failed`);
      throw new Error(errorMessage);
    }
  } catch (error) {
    // Cache error
    const errorMessage = error instanceof Error ? error.message : 'Unknown validation error';
    apiKeyValidationCache = {
      isValid: false,
      timestamp: currentTime,
      error: errorMessage
    };
    console.error(`[${timestamp}] [OPENAI_CLIENT] ❌ OpenAI configuration validation error:`, error);
    throw error;
  }
}

/**
 * Reset validation cache (useful for testing or manual refresh)
 */
export function resetValidationCache(): void {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [OPENAI_CLIENT] 🔄 Resetting API key validation cache`);
  apiKeyValidationCache = null;
}

/**
 * Get validation cache status
 */
export function getValidationCacheStatus(): {
  hasCache: boolean;
  isValid?: boolean;
  age?: number;
  expiresIn?: number;
} {
  if (!apiKeyValidationCache) {
    return { hasCache: false };
  }

  const currentTime = Date.now();
  const age = currentTime - apiKeyValidationCache.timestamp;
  const expiresIn = VALIDATION_CACHE_DURATION - age;

  return {
    hasCache: true,
    isValid: apiKeyValidationCache.isValid,
    age: Math.round(age / 1000), // in seconds
    expiresIn: Math.round(expiresIn / 1000) // in seconds
  };
}
